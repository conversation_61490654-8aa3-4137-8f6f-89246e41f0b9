/**
 * Claude-Flow v2.0.0 SQLite Memory Schema
 * 12-table schema for enhanced memory management
 */

export const CLAUDE_FLOW_MEMORY_SCHEMA = {
  version: '2.0.0',
  tables: {
    // ============= Core Tables =============
    
    /**
     * 1. Agents Table - Stores all agent instances
     */
    agents: `
      CREATE TABLE IF NOT EXISTS agents (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        status TEXT NOT NULL,
        swarm_id TEXT,
        parent_id TEXT,
        capabilities TEXT, -- JSON array
        system_prompt TEXT,
        max_concurrent_tasks INTEGER DEFAULT 3,
        priority INTEGER DEFAULT 5,
        resources TEXT, -- JSON object
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_activity TIMESTAMP,
        metadata TEXT, -- JSON object
        
        FOREIGN KEY (swarm_id) REFERENCES swarms(id),
        FOREIGN KEY (parent_id) REFERENCES agents(id)
      );
      
      CREATE INDEX idx_agents_type ON agents(type);
      CREATE INDEX idx_agents_status ON agents(status);
      CREATE INDEX idx_agents_swarm ON agents(swarm_id);
    `,
    
    /**
     * 2. Memories Table - Core memory storage
     */
    memories: `
      CREATE TABLE IF NOT EXISTS memories (
        id TEXT PRIMARY KEY,
        agent_id TEXT NOT NULL,
        session_id TEXT NOT NULL,
        namespace_id TEXT,
        type TEXT NOT NULL,
        content TEXT NOT NULL,
        context TEXT, -- JSON object
        tags TEXT, -- JSON array
        embedding BLOB, -- Vector embedding for similarity search
        importance REAL DEFAULT 0.5,
        access_count INTEGER DEFAULT 0,
        last_accessed TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        compressed BOOLEAN DEFAULT FALSE,
        encrypted BOOLEAN DEFAULT FALSE,
        
        FOREIGN KEY (agent_id) REFERENCES agents(id),
        FOREIGN KEY (namespace_id) REFERENCES namespaces(id)
      );
      
      CREATE INDEX idx_memories_agent ON memories(agent_id);
      CREATE INDEX idx_memories_type ON memories(type);
      CREATE INDEX idx_memories_namespace ON memories(namespace_id);
      CREATE INDEX idx_memories_importance ON memories(importance DESC);
      CREATE INDEX idx_memories_created ON memories(created_at DESC);
    `,
    
    /**
     * 3. Tasks Table - Task management
     */
    tasks: `
      CREATE TABLE IF NOT EXISTS tasks (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        description TEXT NOT NULL,
        status TEXT NOT NULL,
        priority INTEGER DEFAULT 5,
        agent_id TEXT,
        objective_id TEXT,
        input TEXT, -- JSON object
        output TEXT, -- JSON object
        error TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        started_at TIMESTAMP,
        completed_at TIMESTAMP,
        timeout INTEGER,
        retry_count INTEGER DEFAULT 0,
        dependencies TEXT, -- JSON array of task IDs
        
        FOREIGN KEY (agent_id) REFERENCES agents(id),
        FOREIGN KEY (objective_id) REFERENCES objectives(id)
      );
      
      CREATE INDEX idx_tasks_status ON tasks(status);
      CREATE INDEX idx_tasks_agent ON tasks(agent_id);
      CREATE INDEX idx_tasks_priority ON tasks(priority DESC);
      CREATE INDEX idx_tasks_created ON tasks(created_at DESC);
    `,
    
    /**
     * 4. Objectives Table - Swarm objectives
     */
    objectives: `
      CREATE TABLE IF NOT EXISTS objectives (
        id TEXT PRIMARY KEY,
        swarm_id TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        strategy TEXT DEFAULT 'adaptive',
        status TEXT NOT NULL,
        tasks TEXT NOT NULL, -- JSON array
        progress REAL DEFAULT 0.0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        started_at TIMESTAMP,
        completed_at TIMESTAMP,
        timeout INTEGER,
        metadata TEXT, -- JSON object
        
        FOREIGN KEY (swarm_id) REFERENCES swarms(id)
      );
      
      CREATE INDEX idx_objectives_swarm ON objectives(swarm_id);
      CREATE INDEX idx_objectives_status ON objectives(status);
    `,
    
    /**
     * 5. Swarms Table - Swarm coordination
     */
    swarms: `
      CREATE TABLE IF NOT EXISTS swarms (
        id TEXT PRIMARY KEY,
        name TEXT,
        queen_agent_id TEXT,
        status TEXT NOT NULL,
        strategy TEXT DEFAULT 'adaptive',
        neural_enabled BOOLEAN DEFAULT TRUE,
        security_level TEXT DEFAULT 'normal',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        metadata TEXT, -- JSON object
        
        FOREIGN KEY (queen_agent_id) REFERENCES agents(id)
      );
      
      CREATE INDEX idx_swarms_status ON swarms(status);
    `,
    
    /**
     * 6. Neural Models Table - Trained neural networks
     */
    neural_models: `
      CREATE TABLE IF NOT EXISTS neural_models (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        architecture TEXT, -- JSON object
        weights BLOB, -- Serialized model weights
        accuracy REAL,
        trained_epochs INTEGER,
        training_data_hash TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_trained_at TIMESTAMP,
        metadata TEXT -- JSON object
      );
      
      CREATE INDEX idx_neural_models_type ON neural_models(type);
      CREATE INDEX idx_neural_models_accuracy ON neural_models(accuracy DESC);
    `,
    
    /**
     * 7. Patterns Table - Recognized patterns
     */
    patterns: `
      CREATE TABLE IF NOT EXISTS patterns (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        pattern TEXT NOT NULL, -- JSON object
        confidence REAL NOT NULL,
        frequency INTEGER DEFAULT 1,
        first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        agent_id TEXT,
        model_id TEXT,
        metadata TEXT, -- JSON object
        
        FOREIGN KEY (agent_id) REFERENCES agents(id),
        FOREIGN KEY (model_id) REFERENCES neural_models(id)
      );
      
      CREATE INDEX idx_patterns_type ON patterns(type);
      CREATE INDEX idx_patterns_confidence ON patterns(confidence DESC);
      CREATE INDEX idx_patterns_frequency ON patterns(frequency DESC);
    `,
    
    /**
     * 8. Messages Table - Inter-agent communication
     */
    messages: `
      CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        content TEXT NOT NULL, -- JSON object
        sender_id TEXT NOT NULL,
        sender_type TEXT NOT NULL,
        priority TEXT DEFAULT 'normal',
        channel TEXT,
        reply_to TEXT,
        delivered BOOLEAN DEFAULT FALSE,
        read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        delivered_at TIMESTAMP,
        ttl INTEGER,
        metadata TEXT, -- JSON object
        
        FOREIGN KEY (sender_id) REFERENCES agents(id),
        FOREIGN KEY (reply_to) REFERENCES messages(id)
      );
      
      CREATE INDEX idx_messages_sender ON messages(sender_id);
      CREATE INDEX idx_messages_priority ON messages(priority);
      CREATE INDEX idx_messages_created ON messages(created_at DESC);
      CREATE INDEX idx_messages_delivered ON messages(delivered);
    `,
    
    /**
     * 9. Message Recipients Table - Message delivery tracking
     */
    message_recipients: `
      CREATE TABLE IF NOT EXISTS message_recipients (
        message_id TEXT NOT NULL,
        recipient_id TEXT NOT NULL,
        recipient_type TEXT NOT NULL,
        delivered BOOLEAN DEFAULT FALSE,
        read BOOLEAN DEFAULT FALSE,
        delivered_at TIMESTAMP,
        read_at TIMESTAMP,
        
        PRIMARY KEY (message_id, recipient_id),
        FOREIGN KEY (message_id) REFERENCES messages(id),
        FOREIGN KEY (recipient_id) REFERENCES agents(id)
      );
      
      CREATE INDEX idx_message_recipients_recipient ON message_recipients(recipient_id);
    `,
    
    /**
     * 10. Resources Table - Resource allocation tracking
     */
    resources: `
      CREATE TABLE IF NOT EXISTS resources (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        name TEXT NOT NULL,
        capacity TEXT NOT NULL, -- JSON object
        allocated TEXT, -- JSON object
        available TEXT, -- JSON object
        agent_id TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        metadata TEXT, -- JSON object
        
        FOREIGN KEY (agent_id) REFERENCES agents(id)
      );
      
      CREATE INDEX idx_resources_type ON resources(type);
      CREATE INDEX idx_resources_agent ON resources(agent_id);
    `,
    
    /**
     * 11. Namespaces Table - Memory organization
     */
    namespaces: `
      CREATE TABLE IF NOT EXISTS namespaces (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        parent_id TEXT,
        permissions TEXT NOT NULL, -- JSON object
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        metadata TEXT, -- JSON object
        
        FOREIGN KEY (parent_id) REFERENCES namespaces(id)
      );
      
      CREATE INDEX idx_namespaces_parent ON namespaces(parent_id);
    `,
    
    /**
     * 12. Metrics Table - Performance and monitoring data
     */
    metrics: `
      CREATE TABLE IF NOT EXISTS metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        type TEXT NOT NULL,
        source_id TEXT,
        source_type TEXT,
        metric_name TEXT NOT NULL,
        metric_value REAL NOT NULL,
        unit TEXT,
        tags TEXT, -- JSON array
        metadata TEXT -- JSON object
      );
      
      CREATE INDEX idx_metrics_timestamp ON metrics(timestamp DESC);
      CREATE INDEX idx_metrics_type ON metrics(type);
      CREATE INDEX idx_metrics_source ON metrics(source_id);
      CREATE INDEX idx_metrics_name ON metrics(metric_name);
    `
  },
  
  // ============= Views =============
  
  views: {
    /**
     * Active agents view
     */
    active_agents: `
      CREATE VIEW IF NOT EXISTS active_agents AS
      SELECT 
        a.*,
        s.name as swarm_name,
        COUNT(DISTINCT t.id) as active_tasks
      FROM agents a
      LEFT JOIN swarms s ON a.swarm_id = s.id
      LEFT JOIN tasks t ON a.id = t.agent_id AND t.status IN ('running', 'queued')
      WHERE a.status IN ('idle', 'running', 'busy', 'active')
      GROUP BY a.id;
    `,
    
    /**
     * Memory usage statistics
     */
    memory_stats: `
      CREATE VIEW IF NOT EXISTS memory_stats AS
      SELECT 
        namespace_id,
        agent_id,
        type,
        COUNT(*) as entry_count,
        SUM(LENGTH(content)) as total_size,
        AVG(importance) as avg_importance,
        MAX(created_at) as latest_entry
      FROM memories
      GROUP BY namespace_id, agent_id, type;
    `,
    
    /**
     * Task performance metrics
     */
    task_performance: `
      CREATE VIEW IF NOT EXISTS task_performance AS
      SELECT 
        type,
        status,
        COUNT(*) as count,
        AVG(CASE 
          WHEN completed_at IS NOT NULL AND started_at IS NOT NULL 
          THEN (julianday(completed_at) - julianday(started_at)) * 86400 
          ELSE NULL 
        END) as avg_duration_seconds,
        AVG(retry_count) as avg_retries
      FROM tasks
      GROUP BY type, status;
    `
  },
  
  // ============= Triggers =============
  
  triggers: {
    /**
     * Update agent last_activity on task assignment
     */
    update_agent_activity: `
      CREATE TRIGGER IF NOT EXISTS update_agent_activity
      AFTER UPDATE ON tasks
      WHEN NEW.agent_id IS NOT NULL AND OLD.status != NEW.status
      BEGIN
        UPDATE agents 
        SET last_activity = CURRENT_TIMESTAMP 
        WHERE id = NEW.agent_id;
      END;
    `,
    
    /**
     * Update memory access tracking
     */
    track_memory_access: `
      CREATE TRIGGER IF NOT EXISTS track_memory_access
      AFTER UPDATE ON memories
      BEGIN
        UPDATE memories 
        SET 
          access_count = access_count + 1,
          last_accessed = CURRENT_TIMESTAMP
        WHERE id = NEW.id;
      END;
    `,
    
    /**
     * Update timestamps
     */
    update_timestamps: `
      CREATE TRIGGER IF NOT EXISTS update_agents_timestamp
      AFTER UPDATE ON agents
      BEGIN
        UPDATE agents SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
      
      CREATE TRIGGER IF NOT EXISTS update_resources_timestamp
      AFTER UPDATE ON resources
      BEGIN
        UPDATE resources SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `
  }
};

/**
 * Migration queries from old schema to new
 */
export const MIGRATION_QUERIES = [
  // Backup existing data
  `CREATE TABLE IF NOT EXISTS memories_backup AS SELECT * FROM memories;`,
  
  // Drop old tables if they exist
  `DROP TABLE IF EXISTS memories;`,
  `DROP TABLE IF EXISTS agents;`,
  `DROP TABLE IF EXISTS tasks;`,
  
  // Create new schema
  ...Object.values(CLAUDE_FLOW_MEMORY_SCHEMA.tables),
  ...Object.values(CLAUDE_FLOW_MEMORY_SCHEMA.views),
  ...Object.values(CLAUDE_FLOW_MEMORY_SCHEMA.triggers)
];

/**
 * Schema validation queries
 */
export const SCHEMA_VALIDATION = {
  checkTables: `
    SELECT name FROM sqlite_master 
    WHERE type='table' 
    ORDER BY name;
  `,
  
  checkIndexes: `
    SELECT name, tbl_name FROM sqlite_master 
    WHERE type='index' 
    ORDER BY name;
  `,
  
  checkViews: `
    SELECT name FROM sqlite_master 
    WHERE type='view' 
    ORDER BY name;
  `,
  
  checkTriggers: `
    SELECT name FROM sqlite_master 
    WHERE type='trigger' 
    ORDER BY name;
  `
};