/**
 * Claude-Flow v2.0.0 Swarm Coordination Library
 * Implements hive-mind coordination algorithms and swarm intelligence
 */

import { ClaudeFlowIntegrationV2 } from './claudeFlowIntegrationV2';
import type {
  <PERSON><PERSON><PERSON>A<PERSON>,
  <PERSON><PERSON><PERSON>Objective,
  <PERSON><PERSON><PERSON>T<PERSON>,
  <PERSON><PERSON><PERSON>SwarmTopology,
  SwarmOptimizationResult
} from '../types/claudeFlowV2';

export interface SwarmCoordinationConfig {
  maxConcurrentTasks: number;
  taskPriorityWeights: {
    critical: number;
    high: number;
    normal: number;
    low: number;
  };
  loadBalancingStrategy: 'round-robin' | 'least-loaded' | 'capability-based' | 'adaptive';
  failoverThreshold: number;
  optimizationInterval: number; // minutes
}

export interface SwarmMetrics {
  efficiency: number;
  throughput: number;
  errorRate: number;
  resourceUtilization: number;
  communicationLatency: number;
  taskCompletionRate: number;
}

export interface LoadBalancingResult {
  redistributedTasks: number;
  balanceScore: number; // 0-100
  recommendations: string[];
  affectedAgents: string[];
}

export interface SwarmHealthCheck {
  overall: number; // 0-100
  components: {
    communication: number;
    taskDistribution: number;
    resourceAllocation: number;
    neuralCoherence: number;
    failureRecovery: number;
  };
  issues: Array<{
    severity: 'low' | 'medium' | 'high' | 'critical';
    component: string;
    description: string;
    recommendation: string;
  }>;
  timestamp: string;
}

export class ClaudeFlowSwarmCoordinator {
  private integration: ClaudeFlowIntegrationV2;
  private config: SwarmCoordinationConfig;
  private optimizationTimer?: NodeJS.Timer;
  private healthCheckTimer?: NodeJS.Timer;
  private lastOptimization?: Date;
  private swarmMetrics: SwarmMetrics = {
    efficiency: 0,
    throughput: 0,
    errorRate: 0,
    resourceUtilization: 0,
    communicationLatency: 0,
    taskCompletionRate: 0
  };

  constructor(integration: ClaudeFlowIntegrationV2, config?: Partial<SwarmCoordinationConfig>) {
    this.integration = integration;
    this.config = {
      maxConcurrentTasks: 50,
      taskPriorityWeights: {
        critical: 10,
        high: 7,
        normal: 5,
        low: 2
      },
      loadBalancingStrategy: 'adaptive',
      failoverThreshold: 0.8,
      optimizationInterval: 15,
      ...config
    };
  }

  /**
   * Initialize swarm coordination
   */
  async initialize(): Promise<void> {
    // Start optimization timer
    this.optimizationTimer = setInterval(
      () => this.performOptimization(),
      this.config.optimizationInterval * 60 * 1000
    );

    // Start health check timer
    this.healthCheckTimer = setInterval(
      () => this.performHealthCheck(),
      30 * 1000 // Every 30 seconds
    );

    console.log('Claude-Flow Swarm Coordinator initialized');
  }

  /**
   * Shutdown coordination timers
   */
  shutdown(): void {
    if (this.optimizationTimer) {
      clearInterval(this.optimizationTimer);
      this.optimizationTimer = undefined;
    }

    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = undefined;
    }

    console.log('Claude-Flow Swarm Coordinator shutdown');
  }

  /**
   * Optimize swarm topology and performance
   */
  async optimizeSwarm(): Promise<SwarmOptimizationResult> {
    try {
      // Get current swarm status
      const status = await this.integration.getSwarmStatus(true);
      const agents = status.agents || [];
      const activeObjectives = status.activeObjectives || [];

      // Analyze current performance
      const metrics = await this.calculateSwarmMetrics(agents, activeObjectives);
      
      // Perform optimization based on current state
      const optimizations: string[] = [];
      let improvementScore = 0;

      // 1. Load balancing optimization
      if (metrics.resourceUtilization > this.config.failoverThreshold) {
        const loadBalanceResult = await this.performLoadBalancing(agents);
        optimizations.push(`Load balancing: redistributed ${loadBalanceResult.redistributedTasks} tasks`);
        improvementScore += loadBalanceResult.balanceScore * 0.3;
      }

      // 2. Agent allocation optimization
      const allocationResult = await this.optimizeAgentAllocation(agents, activeObjectives);
      optimizations.push(`Agent allocation: ${allocationResult.changes} changes made`);
      improvementScore += allocationResult.improvementScore * 0.4;

      // 3. Communication optimization
      const commResult = await this.optimizeCommunication(agents);
      optimizations.push(`Communication: ${commResult.optimizations} optimizations applied`);
      improvementScore += commResult.improvementScore * 0.3;

      this.lastOptimization = new Date();
      this.swarmMetrics = metrics;

      return {
        success: true,
        improvementScore: Math.min(improvementScore, 100),
        optimizations,
        metrics,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Swarm optimization failed:', error);
      return {
        success: false,
        improvementScore: 0,
        optimizations: [],
        metrics: this.swarmMetrics,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Perform intelligent load balancing
   */
  async performLoadBalancing(agents: ClaudeFlowAgent[]): Promise<LoadBalancingResult> {
    // Calculate current load distribution
    const agentLoads = await this.calculateAgentLoads(agents);
    
    // Find overloaded and underloaded agents
    const overloaded = agentLoads.filter(a => a.load > 0.8);
    const underloaded = agentLoads.filter(a => a.load < 0.3 && a.agent.status === 'idle');

    let redistributedTasks = 0;
    const affectedAgents: string[] = [];
    const recommendations: string[] = [];

    // Redistribute tasks from overloaded to underloaded agents
    for (const overloadedAgent of overloaded) {
      const compatibleUnderloaded = underloaded.filter(u => 
        this.areAgentsCompatible(overloadedAgent.agent, u.agent)
      );

      if (compatibleUnderloaded.length > 0) {
        // Move tasks to least loaded compatible agent
        const target = compatibleUnderloaded.reduce((min, curr) => 
          curr.load < min.load ? curr : min
        );

        const tasksToMove = Math.ceil((overloadedAgent.load - 0.7) * overloadedAgent.activeTasks);
        
        try {
          await this.integration.reassignTasks(
            overloadedAgent.agent.id,
            target.agent.id,
            tasksToMove
          );
          
          redistributedTasks += tasksToMove;
          affectedAgents.push(overloadedAgent.agent.id, target.agent.id);
          
          recommendations.push(
            `Moved ${tasksToMove} tasks from ${overloadedAgent.agent.name} to ${target.agent.name}`
          );
        } catch (error) {
          recommendations.push(
            `Failed to redistribute tasks from ${overloadedAgent.agent.name}: ${error}`
          );
        }
      } else {
        recommendations.push(
          `Consider spawning new agent with capabilities: ${overloadedAgent.agent.capabilities.join(', ')}`
        );
      }
    }

    // Calculate balance score
    const finalLoads = await this.calculateAgentLoads(agents);
    const loadVariance = this.calculateLoadVariance(finalLoads);
    const balanceScore = Math.max(0, 100 - (loadVariance * 100));

    return {
      redistributedTasks,
      balanceScore,
      recommendations,
      affectedAgents
    };
  }

  /**
   * Optimize agent allocation for objectives
   */
  private async optimizeAgentAllocation(
    agents: ClaudeFlowAgent[],
    objectives: ClaudeFlowObjective[]
  ): Promise<{ changes: number; improvementScore: number }> {
    let changes = 0;
    let totalImprovementScore = 0;

    for (const objective of objectives) {
      if (objective.status === 'running') {
        // Find optimal agents for each task type
        const taskTypes = new Set(objective.tasks.map(t => t.type));
        
        for (const taskType of taskTypes) {
          const currentAgents = agents.filter(a => 
            a.type === taskType || a.capabilities.includes(taskType)
          );
          
          const optimalAgents = await this.findOptimalAgents(taskType, agents);
          
          // Reassign if better agents are available
          for (const optimalAgent of optimalAgents.slice(0, 2)) {
            if (!currentAgents.includes(optimalAgent) && optimalAgent.status === 'idle') {
              try {
                await this.integration.assignAgentToObjective(
                  optimalAgent.id,
                  objective.id,
                  taskType
                );
                changes++;
                totalImprovementScore += 20; // Each reassignment adds potential improvement
              } catch (error) {
                console.warn(`Failed to reassign agent ${optimalAgent.id}:`, error);
              }
            }
          }
        }
      }
    }

    return {
      changes,
      improvementScore: Math.min(totalImprovementScore / Math.max(objectives.length, 1), 100)
    };
  }

  /**
   * Optimize inter-agent communication
   */
  private async optimizeCommunication(
    agents: ClaudeFlowAgent[]
  ): Promise<{ optimizations: number; improvementScore: number }> {
    // Analyze communication patterns
    const commPatterns = await this.analyzeCommunicationPatterns(agents);
    
    let optimizations = 0;
    let improvementScore = 0;

    // Optimize message routing
    if (commPatterns.latency > 100) { // ms
      await this.optimizeMessageRouting(agents);
      optimizations++;
      improvementScore += 30;
    }

    // Reduce communication overhead
    if (commPatterns.overhead > 0.2) {
      await this.reduceCommunicationOverhead(agents);
      optimizations++;
      improvementScore += 25;
    }

    // Implement communication protocols
    if (commPatterns.efficiency < 0.8) {
      await this.implementEfficientProtocols(agents);
      optimizations++;
      improvementScore += 35;
    }

    return { optimizations, improvementScore };
  }

  /**
   * Calculate comprehensive swarm metrics
   */
  private async calculateSwarmMetrics(
    agents: ClaudeFlowAgent[],
    objectives: ClaudeFlowObjective[]
  ): Promise<SwarmMetrics> {
    const activeAgents = agents.filter(a => 
      ['running', 'busy', 'active'].includes(a.status)
    );
    
    const completedTasks = objectives.reduce((total, obj) => 
      total + obj.tasks.filter(t => t.status === 'completed').length, 0
    );
    
    const totalTasks = objectives.reduce((total, obj) => total + obj.tasks.length, 0);
    
    // Get performance metrics from integration
    const perfReport = await this.integration.getPerformanceReport('1h');
    
    return {
      efficiency: activeAgents.length / Math.max(agents.length, 1) * 100,
      throughput: completedTasks,
      errorRate: perfReport.metrics.errorRate,
      resourceUtilization: perfReport.metrics.tokenUsage / 100000 * 100, // Normalize
      communicationLatency: perfReport.metrics.avgResponseTime,
      taskCompletionRate: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0
    };
  }

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck(): Promise<SwarmHealthCheck> {
    try {
      const status = await this.integration.getSwarmStatus(true);
      const agents = status.agents || [];
      
      // Calculate component health scores
      const communication = await this.calculateCommunicationHealth(agents);
      const taskDistribution = await this.calculateTaskDistributionHealth(agents);
      const resourceAllocation = await this.calculateResourceHealth(agents);
      const neuralCoherence = await this.calculateNeuralCoherenceHealth();
      const failureRecovery = await this.calculateFailureRecoveryHealth(agents);
      
      const overall = (communication + taskDistribution + resourceAllocation + 
                      neuralCoherence + failureRecovery) / 5;

      // Identify issues
      const issues = [];
      
      if (communication < 70) {
        issues.push({
          severity: communication < 40 ? 'critical' : 'high' as const,
          component: 'communication',
          description: 'Inter-agent communication latency is high',
          recommendation: 'Optimize message routing and reduce communication overhead'
        });
      }
      
      if (taskDistribution < 70) {
        issues.push({
          severity: taskDistribution < 40 ? 'critical' : 'medium' as const,
          component: 'task-distribution',
          description: 'Task distribution is unbalanced across agents',
          recommendation: 'Perform load balancing and agent reallocation'
        });
      }
      
      if (resourceAllocation < 70) {
        issues.push({
          severity: 'medium' as const,
          component: 'resource-allocation',
          description: 'Resource utilization is suboptimal',
          recommendation: 'Review agent resource assignments and scale appropriately'
        });
      }

      return {
        overall,
        components: {
          communication,
          taskDistribution,
          resourceAllocation,
          neuralCoherence,
          failureRecovery
        },
        issues,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        overall: 0,
        components: {
          communication: 0,
          taskDistribution: 0,
          resourceAllocation: 0,
          neuralCoherence: 0,
          failureRecovery: 0
        },
        issues: [{
          severity: 'critical',
          component: 'system',
          description: 'Health check system failure',
          recommendation: 'Check swarm coordinator and integration connectivity'
        }],
        timestamp: new Date().toISOString()
      };
    }
  }

  // ============= Private Helper Methods =============

  private async performOptimization(): Promise<void> {
    try {
      const result = await this.optimizeSwarm();
      if (result.success) {
        console.log('Swarm optimization completed:', {
          improvementScore: result.improvementScore,
          optimizations: result.optimizations.length
        });
      }
    } catch (error) {
      console.error('Periodic optimization failed:', error);
    }
  }

  private async calculateAgentLoads(agents: ClaudeFlowAgent[]): Promise<Array<{
    agent: ClaudeFlowAgent;
    load: number;
    activeTasks: number;
  }>> {
    const loads = [];
    
    for (const agent of agents) {
      // Get active tasks for this agent
      const activeTasks = await this.getActiveTasksForAgent(agent.id);
      const load = activeTasks / Math.max(agent.maxConcurrentTasks, 1);
      
      loads.push({
        agent,
        load: Math.min(load, 1),
        activeTasks
      });
    }
    
    return loads;
  }

  private async getActiveTasksForAgent(agentId: string): Promise<number> {
    try {
      // This would typically call the MCP integration to get actual task counts
      // For now, we'll simulate based on agent status
      const response = await this.integration.getAgentTasks(agentId);
      return response.filter((task: ClaudeFlowTask) => 
        ['running', 'assigned', 'queued'].includes(task.status)
      ).length;
    } catch (error) {
      return 0; // Fallback
    }
  }

  private areAgentsCompatible(agent1: ClaudeFlowAgent, agent2: ClaudeFlowAgent): boolean {
    // Check if agents have overlapping capabilities
    return agent1.capabilities.some(cap => agent2.capabilities.includes(cap)) ||
           agent1.type === agent2.type;
  }

  private calculateLoadVariance(loads: Array<{ load: number }>): number {
    const mean = loads.reduce((sum, l) => sum + l.load, 0) / loads.length;
    const variance = loads.reduce((sum, l) => sum + Math.pow(l.load - mean, 2), 0) / loads.length;
    return Math.sqrt(variance);
  }

  private async findOptimalAgents(taskType: string, agents: ClaudeFlowAgent[]): Promise<ClaudeFlowAgent[]> {
    // Score agents based on capabilities, availability, and performance
    const scoredAgents = agents.map(agent => ({
      agent,
      score: this.calculateAgentScore(agent, taskType)
    }));
    
    return scoredAgents
      .sort((a, b) => b.score - a.score)
      .map(sa => sa.agent);
  }

  private calculateAgentScore(agent: ClaudeFlowAgent, taskType: string): number {
    let score = 0;
    
    // Capability match
    if (agent.type === taskType) score += 50;
    if (agent.capabilities.includes(taskType)) score += 30;
    
    // Availability
    if (agent.status === 'idle') score += 20;
    else if (agent.status === 'running') score += 10;
    
    // Priority weight
    score += agent.priority;
    
    // Recent activity (prefer recently active agents)
    if (agent.lastActivity) {
      const timeSinceActivity = Date.now() - new Date(agent.lastActivity).getTime();
      const hoursSinceActivity = timeSinceActivity / (1000 * 60 * 60);
      score += Math.max(0, 10 - hoursSinceActivity); // Decay over hours
    }
    
    return score;
  }

  private async analyzeCommunicationPatterns(agents: ClaudeFlowAgent[]): Promise<{
    latency: number;
    overhead: number;
    efficiency: number;
  }> {
    try {
      const messageMetrics = await this.integration.getMessageMetrics();
      return {
        latency: messageMetrics.averageLatency || 0,
        overhead: messageMetrics.overhead || 0,
        efficiency: messageMetrics.efficiency || 0
      };
    } catch (error) {
      return { latency: 0, overhead: 0, efficiency: 1 };
    }
  }

  private async optimizeMessageRouting(agents: ClaudeFlowAgent[]): Promise<void> {
    // Implementation would optimize message routing algorithms
    console.log('Optimizing message routing for', agents.length, 'agents');
  }

  private async reduceCommunicationOverhead(agents: ClaudeFlowAgent[]): Promise<void> {
    // Implementation would reduce unnecessary communication
    console.log('Reducing communication overhead');
  }

  private async implementEfficientProtocols(agents: ClaudeFlowAgent[]): Promise<void> {
    // Implementation would deploy more efficient communication protocols
    console.log('Implementing efficient communication protocols');
  }

  private async calculateCommunicationHealth(agents: ClaudeFlowAgent[]): Promise<number> {
    const patterns = await this.analyzeCommunicationPatterns(agents);
    const latencyScore = Math.max(0, 100 - patterns.latency / 10);
    const overheadScore = Math.max(0, 100 - patterns.overhead * 100);
    const efficiencyScore = patterns.efficiency * 100;
    
    return (latencyScore + overheadScore + efficiencyScore) / 3;
  }

  private async calculateTaskDistributionHealth(agents: ClaudeFlowAgent[]): Promise<number> {
    const loads = await this.calculateAgentLoads(agents);
    const variance = this.calculateLoadVariance(loads);
    return Math.max(0, 100 - variance * 100);
  }

  private async calculateResourceHealth(agents: ClaudeFlowAgent[]): Promise<number> {
    // Calculate based on resource utilization efficiency
    const perfReport = await this.integration.getPerformanceReport('1h');
    return Math.min(100, (1 - perfReport.metrics.errorRate) * 100);
  }

  private async calculateNeuralCoherenceHealth(): Promise<number> {
    // Calculate neural network coherence across the swarm
    try {
      const neuralMetrics = await this.integration.getNeuralNetworkMetrics();
      return neuralMetrics.coherenceScore * 100;
    } catch (error) {
      return 75; // Default assumption
    }
  }

  private async calculateFailureRecoveryHealth(agents: ClaudeFlowAgent[]): Promise<number> {
    const errorAgents = agents.filter(a => a.status === 'error' || a.status === 'failed');
    const errorRate = errorAgents.length / Math.max(agents.length, 1);
    return Math.max(0, 100 - errorRate * 100);
  }

  /**
   * Get current swarm metrics
   */
  getMetrics(): SwarmMetrics {
    return { ...this.swarmMetrics };
  }

  /**
   * Get last optimization timestamp
   */
  getLastOptimization(): Date | undefined {
    return this.lastOptimization;
  }
}