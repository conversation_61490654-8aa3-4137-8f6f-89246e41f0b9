/**
 * Claude-Flow v2.0.0 Memory Manager
 * Handles SQLite database operations for the 12-table schema
 */

import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { CLAUDE_FLOW_MEMORY_SCHEMA, MIGRATION_QUERIES, SCHEMA_VALIDATION } from './claudeFlowMemorySchema';
import type { 
  ClaudeFlowMemoryEntry, 
  MemoryNamespace, 
  MemoryQueryOptions,
  MemoryTable
} from '../types/claudeFlowV2';

export interface MemoryManagerConfig {
  databasePath?: string;
  enableCompression?: boolean;
  enableEncryption?: boolean;
  backupInterval?: number; // minutes
  maxMemorySize?: number; // MB
}

export class ClaudeFlowMemoryManager {
  private db: Database.Database;
  private config: MemoryManagerConfig;
  private backupTimer?: NodeJS.Timer;
  
  constructor(config: MemoryManagerConfig = {}) {
    this.config = {
      databasePath: config.databasePath || path.join(process.cwd(), '.claude-flow', 'memory.db'),
      enableCompression: config.enableCompression ?? true,
      enableEncryption: config.enableEncryption ?? true,
      backupInterval: config.backupInterval ?? 60,
      maxMemorySize: config.maxMemorySize ?? 1000
    };
    
    // Ensure directory exists
    const dbDir = path.dirname(this.config.databasePath);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }
    
    // Initialize database
    this.db = new Database(this.config.databasePath);
    this.db.pragma('journal_mode = WAL');
    this.db.pragma('synchronous = NORMAL');
    
    // Initialize schema
    this.initializeSchema();
    
    // Start backup timer
    if (this.config.backupInterval > 0) {
      this.startBackupTimer();
    }
  }
  
  /**
   * Initialize the database schema
   */
  private initializeSchema(): void {
    try {
      // Check if migration is needed
      const tables = this.db.prepare(SCHEMA_VALIDATION.checkTables).all();
      const existingTables = tables.map((t: any) => t.name);
      
      if (existingTables.includes('memories') && !existingTables.includes('neural_models')) {
        console.log('Migrating to Claude-Flow v2.0.0 schema...');
        this.migrateSchema();
      } else if (existingTables.length === 0) {
        console.log('Creating Claude-Flow v2.0.0 schema...');
        this.createSchema();
      }
      
      // Validate schema
      this.validateSchema();
    } catch (error) {
      console.error('Failed to initialize memory schema:', error);
      throw error;
    }
  }
  
  /**
   * Create the full schema
   */
  private createSchema(): void {
    const transaction = this.db.transaction(() => {
      // Create tables
      Object.values(CLAUDE_FLOW_MEMORY_SCHEMA.tables).forEach(query => {
        this.db.exec(query);
      });
      
      // Create views
      Object.values(CLAUDE_FLOW_MEMORY_SCHEMA.views).forEach(query => {
        this.db.exec(query);
      });
      
      // Create triggers
      Object.values(CLAUDE_FLOW_MEMORY_SCHEMA.triggers).forEach(query => {
        this.db.exec(query);
      });
    });
    
    transaction();
  }
  
  /**
   * Migrate from old schema to new
   */
  private migrateSchema(): void {
    const transaction = this.db.transaction(() => {
      MIGRATION_QUERIES.forEach(query => {
        try {
          this.db.exec(query);
        } catch (error) {
          console.warn('Migration query failed (may be expected):', error);
        }
      });
    });
    
    transaction();
    
    // Migrate old data if backup exists
    const hasBackup = this.db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='memories_backup'").get();
    if (hasBackup) {
      this.migrateOldMemories();
    }
  }
  
  /**
   * Migrate old memory entries to new schema
   */
  private migrateOldMemories(): void {
    try {
      const oldMemories = this.db.prepare('SELECT * FROM memories_backup').all();
      const insertStmt = this.db.prepare(`
        INSERT INTO memories (id, agent_id, session_id, type, content, context, tags, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      const transaction = this.db.transaction(() => {
        for (const memory of oldMemories) {
          insertStmt.run(
            memory.id,
            memory.agentId || memory.agent_id,
            memory.sessionId || memory.session_id,
            memory.type,
            memory.content,
            JSON.stringify(memory.context || {}),
            JSON.stringify(memory.tags || []),
            memory.timestamp || memory.created_at
          );
        }
      });
      
      transaction();
      console.log(`Migrated ${oldMemories.length} memory entries`);
      
      // Drop backup table
      this.db.exec('DROP TABLE memories_backup');
    } catch (error) {
      console.error('Failed to migrate old memories:', error);
    }
  }
  
  /**
   * Validate schema integrity
   */
  private validateSchema(): void {
    const requiredTables = [
      'agents', 'memories', 'tasks', 'objectives', 'swarms',
      'neural_models', 'patterns', 'messages', 'message_recipients',
      'resources', 'namespaces', 'metrics'
    ];
    
    const tables = this.db.prepare(SCHEMA_VALIDATION.checkTables).all();
    const existingTables = tables.map((t: any) => t.name);
    
    const missingTables = requiredTables.filter(t => !existingTables.includes(t));
    if (missingTables.length > 0) {
      throw new Error(`Missing required tables: ${missingTables.join(', ')}`);
    }
    
    console.log('Claude-Flow v2.0.0 schema validated successfully');
  }
  
  // ============= Memory Operations =============
  
  /**
   * Store a memory entry
   */
  async storeMemory(entry: Omit<ClaudeFlowMemoryEntry, 'id'>): Promise<string> {
    const id = this.generateId('mem');
    
    // Compress content if enabled
    let content = entry.content;
    if (this.config.enableCompression && content.length > 1000) {
      content = await this.compressContent(content);
    }
    
    const stmt = this.db.prepare(`
      INSERT INTO memories (
        id, agent_id, session_id, namespace_id, type, content, 
        context, tags, importance, compressed, encrypted
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run(
      id,
      entry.agentId,
      entry.sessionId,
      entry.namespace || null,
      entry.type,
      content,
      JSON.stringify(entry.context || {}),
      JSON.stringify(entry.tags || []),
      0.5, // default importance
      this.config.enableCompression && content.length > 1000,
      this.config.enableEncryption || false
    );
    
    return id;
  }
  
  /**
   * Query memories with advanced filters
   */
  async queryMemories(options: MemoryQueryOptions): Promise<ClaudeFlowMemoryEntry[]> {
    let query = 'SELECT * FROM memories WHERE 1=1';
    const params: any[] = [];
    
    // Build query conditions
    if (options.namespace) {
      query += ' AND namespace_id = ?';
      params.push(options.namespace);
    }
    
    if (options.filters) {
      for (const filter of options.filters) {
        switch (filter.operator) {
          case 'eq':
            query += ` AND ${filter.field} = ?`;
            params.push(filter.value);
            break;
          case 'like':
            query += ` AND ${filter.field} LIKE ?`;
            params.push(`%${filter.value}%`);
            break;
          case 'in':
            const placeholders = filter.value.map(() => '?').join(',');
            query += ` AND ${filter.field} IN (${placeholders})`;
            params.push(...filter.value);
            break;
          // Add more operators as needed
        }
      }
    }
    
    // Add ordering
    if (options.orderBy) {
      query += ` ORDER BY ${options.orderBy.field} ${options.orderBy.direction}`;
    } else {
      query += ' ORDER BY created_at DESC';
    }
    
    // Add pagination
    if (options.limit) {
      query += ' LIMIT ?';
      params.push(options.limit);
    }
    
    if (options.offset) {
      query += ' OFFSET ?';
      params.push(options.offset);
    }
    
    const rows = this.db.prepare(query).all(...params);
    
    // Transform results
    return rows.map(row => this.transformMemoryRow(row));
  }
  
  /**
   * Create or update a namespace
   */
  async createNamespace(namespace: {
    name: string;
    parent?: string;
    permissions: {
      read: string[];
      write: string[];
      delete: string[];
    };
  }): Promise<string> {
    const id = this.generateId('ns');
    
    const stmt = this.db.prepare(`
      INSERT INTO namespaces (id, name, parent_id, permissions)
      VALUES (?, ?, ?, ?)
    `);
    
    stmt.run(
      id,
      namespace.name,
      namespace.parent || null,
      JSON.stringify(namespace.permissions)
    );
    
    return id;
  }
  
  /**
   * Get memory statistics
   */
  async getMemoryStats(): Promise<any> {
    const stats = {
      totalEntries: 0,
      byType: {} as Record<string, number>,
      byNamespace: {} as Record<string, number>,
      byAgent: {} as Record<string, number>,
      totalSize: 0,
      oldestEntry: null as string | null,
      newestEntry: null as string | null
    };
    
    // Total entries
    const countResult = this.db.prepare('SELECT COUNT(*) as count FROM memories').get();
    stats.totalEntries = countResult.count;
    
    // By type
    const typeStats = this.db.prepare(`
      SELECT type, COUNT(*) as count 
      FROM memories 
      GROUP BY type
    `).all();
    
    typeStats.forEach((row: any) => {
      stats.byType[row.type] = row.count;
    });
    
    // By namespace
    const namespaceStats = this.db.prepare(`
      SELECT n.name, COUNT(m.id) as count
      FROM namespaces n
      LEFT JOIN memories m ON n.id = m.namespace_id
      GROUP BY n.id
    `).all();
    
    namespaceStats.forEach((row: any) => {
      stats.byNamespace[row.name] = row.count;
    });
    
    // Total size
    const sizeResult = this.db.prepare(`
      SELECT SUM(LENGTH(content)) as total_size
      FROM memories
    `).get();
    stats.totalSize = sizeResult.total_size || 0;
    
    // Date range
    const dateRange = this.db.prepare(`
      SELECT 
        MIN(created_at) as oldest,
        MAX(created_at) as newest
      FROM memories
    `).get();
    
    stats.oldestEntry = dateRange.oldest;
    stats.newestEntry = dateRange.newest;
    
    return stats;
  }
  
  // ============= Backup & Restore =============
  
  /**
   * Create a backup of the database
   */
  async createBackup(): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(
      path.dirname(this.config.databasePath!),
      `backup-${timestamp}.db`
    );
    
    await this.db.backup(backupPath);
    
    // Clean old backups (keep last 5)
    this.cleanOldBackups();
    
    return backupPath;
  }
  
  /**
   * Restore from backup
   */
  async restoreFromBackup(backupPath: string): Promise<void> {
    if (!fs.existsSync(backupPath)) {
      throw new Error(`Backup file not found: ${backupPath}`);
    }
    
    // Close current database
    this.db.close();
    
    // Copy backup over current database
    fs.copyFileSync(backupPath, this.config.databasePath!);
    
    // Reopen database
    this.db = new Database(this.config.databasePath!);
    this.db.pragma('journal_mode = WAL');
    
    // Validate restored schema
    this.validateSchema();
  }
  
  /**
   * Start automatic backup timer
   */
  private startBackupTimer(): void {
    this.backupTimer = setInterval(async () => {
      try {
        await this.createBackup();
        console.log('Automatic backup completed');
      } catch (error) {
        console.error('Automatic backup failed:', error);
      }
    }, this.config.backupInterval! * 60 * 1000);
  }
  
  /**
   * Clean old backup files
   */
  private cleanOldBackups(): void {
    const backupDir = path.dirname(this.config.databasePath!);
    const files = fs.readdirSync(backupDir)
      .filter(f => f.startsWith('backup-') && f.endsWith('.db'))
      .sort()
      .reverse();
    
    // Keep only the 5 most recent backups
    files.slice(5).forEach(file => {
      fs.unlinkSync(path.join(backupDir, file));
    });
  }
  
  // ============= Helper Methods =============
  
  /**
   * Generate unique ID
   */
  private generateId(prefix: string): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * Transform database row to memory entry
   */
  private transformMemoryRow(row: any): ClaudeFlowMemoryEntry {
    return {
      id: row.id,
      agentId: row.agent_id,
      sessionId: row.session_id,
      namespace: row.namespace_id,
      type: row.type,
      content: row.compressed ? this.decompressContent(row.content) : row.content,
      context: JSON.parse(row.context || '{}'),
      tags: JSON.parse(row.tags || '[]'),
      timestamp: row.created_at,
      compressed: row.compressed || false,
      encryptionStatus: row.encrypted ? 'encrypted' : 'none'
    };
  }
  
  /**
   * Compress content (placeholder - implement actual compression)
   */
  private async compressContent(content: string): Promise<string> {
    // TODO: Implement actual compression (e.g., using zlib)
    return content;
  }
  
  /**
   * Decompress content (placeholder - implement actual decompression)
   */
  private decompressContent(content: string): string {
    // TODO: Implement actual decompression
    return content;
  }
  
  /**
   * Close database connection
   */
  close(): void {
    if (this.backupTimer) {
      clearInterval(this.backupTimer);
    }
    this.db.close();
  }
  
  // ============= Performance Optimization =============
  
  /**
   * Optimize database performance
   */
  async optimize(): Promise<void> {
    this.db.pragma('optimize');
    this.db.exec('VACUUM');
    this.db.exec('ANALYZE');
  }
  
  /**
   * Get database size info
   */
  getDatabaseInfo(): any {
    const pageCount = this.db.pragma('page_count')[0];
    const pageSize = this.db.pragma('page_size')[0];
    const totalSize = pageCount * pageSize;
    
    return {
      totalSize,
      totalSizeMB: totalSize / (1024 * 1024),
      pageCount,
      pageSize,
      walSize: fs.existsSync(this.config.databasePath + '-wal') 
        ? fs.statSync(this.config.databasePath + '-wal').size 
        : 0
    };
  }
}