/**
 * Claude-Flow v2.0.0 Type Definitions
 * Complete type system for all 87 MCP tools and features
 */

// ============= Core Types =============

export type AgentType = 
  | 'queen' 
  | 'architect' 
  | 'coder' 
  | 'tester' 
  | 'analyst' 
  | 'researcher' 
  | 'security' 
  | 'devops' 
  | 'coordinator' 
  | 'specialist' 
  | 'monitor' 
  | 'optimizer' 
  | 'documenter' 
  | 'reviewer';

export type AgentStatus = 
  | 'idle' 
  | 'running' 
  | 'paused' 
  | 'error' 
  | 'terminated' 
  | 'busy' 
  | 'active' 
  | 'failed';

export type TaskStatus = 
  | 'pending' 
  | 'queued' 
  | 'assigned' 
  | 'running' 
  | 'completed' 
  | 'failed' 
  | 'cancelled';

export type Priority = 'low' | 'normal' | 'high' | 'critical';

export type Strategy = 'parallel' | 'sequential' | 'adaptive';

// ============= Swarm Types =============

export interface SwarmCoordinator {
  id: string;
  swarmId: string;
  queenAgentId?: string;
  strategy: Strategy;
  objectives: string[];
  activeAgents: number;
  createdAt: string;
}

export interface SwarmTopology {
  nodes: Array<{
    agentId: string;
    role: AgentType;
    connections: string[];
    load: number;
  }>;
  edges: Array<{
    from: string;
    to: string;
    type: 'command' | 'data' | 'coordination';
    bandwidth: number;
  }>;
}

export interface SwarmObjectiveTask {
  id?: string;
  type: string;
  description: string;
  requirements?: {
    skills?: string[];
    resources?: ResourceRequirements;
    dependencies?: string[];
  };
  priority: Priority;
  assignedAgentId?: string;
  status?: TaskStatus;
  result?: any;
}

export interface ResourceRequirements {
  cpu?: number;
  memory?: number;
  disk?: number;
  network?: number;
  gpu?: boolean;
}

// ============= Neural Network Types =============

export interface NeuralModelConfig {
  architecture: 'feedforward' | 'cnn' | 'rnn' | 'lstm' | 'transformer';
  layers: Array<{
    type: string;
    units: number;
    activation?: string;
  }>;
  optimizer: 'adam' | 'sgd' | 'rmsprop';
  loss: string;
  metrics: string[];
}

export interface NeuralTrainingResult {
  modelId: string;
  accuracy: number;
  loss: number;
  epochs: number;
  trainingTime: number;
  validationMetrics?: {
    precision?: number;
    recall?: number;
    f1Score?: number;
  };
}

export interface CognitivePattern {
  id: string;
  type: 'behavioral' | 'structural' | 'temporal' | 'causal';
  confidence: number;
  frequency: number;
  context: any;
  insights: string[];
}

// ============= DAA Types =============

export interface DynamicAgentConfig {
  type: string;
  capabilities: string[];
  resources?: ResourceRequirements;
  constraints?: {
    maxTasks?: number;
    timeout?: number;
    securityLevel?: 'low' | 'normal' | 'high';
  };
  learningEnabled?: boolean;
  adaptiveStrategy?: boolean;
}

export interface AgentLifecycleState {
  phase: 'initializing' | 'ready' | 'working' | 'learning' | 'adapting' | 'terminating';
  health: 'healthy' | 'degraded' | 'critical';
  performance: {
    tasksCompleted: number;
    successRate: number;
    avgResponseTime: number;
  };
}

export interface ConsensusRequest {
  topic: string;
  participants: string[];
  votingStrategy: 'majority' | 'unanimous' | 'weighted';
  timeout: number;
}

// ============= Memory Types =============

export interface MemoryNamespace {
  id: string;
  name: string;
  parent?: string;
  permissions: {
    read: string[];
    write: string[];
    delete: string[];
  };
  metadata: {
    created: string;
    modified: string;
    size: number;
    entryCount: number;
  };
}

export interface MemoryTable {
  name: string;
  schema: {
    [column: string]: {
      type: 'TEXT' | 'INTEGER' | 'REAL' | 'BLOB' | 'JSON';
      nullable?: boolean;
      unique?: boolean;
      index?: boolean;
    };
  };
  indexes: string[];
  triggers?: string[];
}

export interface MemoryQueryOptions {
  namespace?: string;
  filters?: Array<{
    field: string;
    operator: 'eq' | 'ne' | 'gt' | 'lt' | 'gte' | 'lte' | 'like' | 'in';
    value: any;
  }>;
  orderBy?: {
    field: string;
    direction: 'asc' | 'desc';
  };
  limit?: number;
  offset?: number;
  includeMetadata?: boolean;
}

// ============= GitHub Integration Types =============

export interface GitHubRepository {
  owner: string;
  name: string;
  url: string;
  defaultBranch: string;
  language: string;
  stars: number;
  forks: number;
  issues: number;
}

export interface GitHubPullRequest {
  id: number;
  title: string;
  author: string;
  state: 'open' | 'closed' | 'merged';
  draft: boolean;
  reviewers: string[];
  labels: string[];
  filesChanged: number;
  additions: number;
  deletions: number;
}

export interface GitHubIssue {
  id: number;
  title: string;
  state: 'open' | 'closed';
  labels: string[];
  assignees: string[];
  milestone?: string;
  priority?: Priority;
}

export interface CodeReviewResult {
  prId: number;
  overallScore: number;
  categories: {
    codeQuality: number;
    security: number;
    performance: number;
    maintainability: number;
    testing: number;
  };
  issues: Array<{
    severity: 'info' | 'warning' | 'error';
    file: string;
    line: number;
    message: string;
    suggestion?: string;
  }>;
  aiRecommendations: string[];
}

// ============= Performance Types =============

export interface PerformanceMetrics {
  timestamp: string;
  system: {
    cpuUsage: number;
    memoryUsage: number;
    diskIO: number;
    networkIO: number;
  };
  swarm: {
    activeAgents: number;
    tasksPerSecond: number;
    avgTaskDuration: number;
    queueLength: number;
  };
  neural: {
    inferencesPerSecond: number;
    avgInferenceTime: number;
    modelCacheHits: number;
  };
}

export interface Bottleneck {
  id: string;
  component: string;
  severity: number; // 0-10
  impact: string;
  metrics: {
    current: number;
    threshold: number;
    trend: 'improving' | 'stable' | 'worsening';
  };
  recommendations: string[];
  autoFixAvailable: boolean;
}

export interface TokenUsageReport {
  period: string;
  totalTokens: number;
  breakdown: {
    input: number;
    output: number;
    system: number;
  };
  byModel: {
    [model: string]: number;
  };
  byAgent: {
    [agentId: string]: number;
  };
  cost: {
    estimated: number;
    currency: string;
  };
}

// ============= Security Types =============

export interface SecurityScanResult {
  scanId: string;
  timestamp: string;
  overallScore: number;
  vulnerabilities: Array<{
    id: string;
    type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    component: string;
    description: string;
    recommendation: string;
    cve?: string;
  }>;
  compliance: {
    [standard: string]: {
      compliant: boolean;
      score: number;
      issues: string[];
    };
  };
}

export interface AgentSandbox {
  agentId: string;
  isolated: boolean;
  resourceLimits: ResourceRequirements;
  networkPolicy: 'none' | 'restricted' | 'full';
  fileSystemAccess: string[];
  allowedOperations: string[];
}

// ============= Workflow Types =============

export interface WorkflowDefinition {
  id: string;
  name: string;
  version: string;
  triggers: Array<{
    type: 'manual' | 'schedule' | 'event' | 'webhook';
    config: any;
  }>;
  steps: Array<{
    id: string;
    name: string;
    type: 'task' | 'decision' | 'parallel' | 'loop';
    config: any;
    dependencies?: string[];
  }>;
  variables: {
    [key: string]: any;
  };
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: string;
  endTime?: string;
  currentStep?: string;
  results: {
    [stepId: string]: any;
  };
  error?: string;
}

// ============= Hook Types =============

export interface HookDefinition {
  name: string;
  type: 'pre' | 'post';
  operation: string;
  enabled: boolean;
  config: {
    command?: string;
    args?: string[];
    timeout?: number;
    continueOnError?: boolean;
  };
  conditions?: Array<{
    field: string;
    operator: string;
    value: any;
  }>;
}

export interface HookExecution {
  hookName: string;
  timestamp: string;
  duration: number;
  success: boolean;
  input: any;
  output?: any;
  error?: string;
  modifications?: any;
}

// ============= Message Bus Types =============

export interface Message {
  id: string;
  type: string;
  content: any;
  sender: {
    id: string;
    type: AgentType;
    swarmId: string;
  };
  receivers: Array<{
    id: string;
    type: AgentType;
    swarmId: string;
  }>;
  priority: Priority;
  timestamp: string;
  channel?: string;
  replyTo?: string;
  ttl?: number;
}

export interface MessageMetrics {
  totalMessages: number;
  messagesByType: {
    [type: string]: number;
  };
  averageDeliveryTime: number;
  failedDeliveries: number;
  queueDepth: number;
  throughput: {
    current: number;
    peak: number;
    average: number;
  };
}

// ============= Alert Types =============

export interface Alert {
  id: string;
  level: 'info' | 'warning' | 'critical';
  source: string;
  message: string;
  timestamp: string;
  acknowledged: boolean;
  assignee?: string;
  metadata?: any;
  actions?: Array<{
    name: string;
    command: string;
    automated: boolean;
  }>;
}

// ============= Monitoring Types =============

export interface MonitoringDashboard {
  widgets: Array<{
    id: string;
    type: 'metric' | 'chart' | 'table' | 'log' | 'alert';
    title: string;
    config: any;
    position: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  }>;
  refreshInterval: number;
  timeRange: string;
}