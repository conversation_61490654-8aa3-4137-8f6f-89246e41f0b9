/**
 * Claude-Flow v2.0.0 Memory Visualization Component
 * Advanced memory pattern visualization and analysis
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain,
  Network,
  Layers,
  Zap,
  Eye,
  Filter,
  Search,
  Download,
  Settings,
  Play,
  Pause,
  SkipForward,
  RotateCcw,
  Maximize2,
  Minimize2,
  Info,
  TrendingUp,
  Clock,
  Tag,
  Users,
  Map,
  Radar,
  GitBranch,
  Activity
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ClaudeFlowMemoryManager } from '@/lib/claudeFlowMemoryManager';
import { ClaudeFlowIntegrationV2, ClaudeFlowMemoryEntry } from '@/lib/claudeFlowIntegrationV2';
import { MemoryQueryOptions } from '@/types/claudeFlowV2';

interface MemoryVisualizationProps {
  memoryManager: ClaudeFlowMemoryManager;
  integration: ClaudeFlowIntegrationV2;
}

interface MemoryNode {
  id: string;
  label: string;
  type: string;
  agentId: string;
  namespace: string;
  size: number;
  connections: string[];
  position: { x: number; y: number };
  color: string;
  importance: number;
  accessCount: number;
  lastAccessed: string;
  tags: string[];
  content: string;
}

interface MemoryPattern {
  id: string;
  type: 'temporal' | 'semantic' | 'structural' | 'causal';
  nodes: string[];
  strength: number;
  frequency: number;
  description: string;
  insights: string[];
}

interface VisualizationMode {
  type: 'network' | 'timeline' | 'hierarchy' | 'heatmap' | 'flow' | '3d';
  config: {
    showConnections: boolean;
    showLabels: boolean;
    showMetadata: boolean;
    colorBy: 'type' | 'agent' | 'importance' | 'age' | 'namespace';
    sizeBy: 'importance' | 'size' | 'access' | 'uniform';
    layout: 'force' | 'circular' | 'hierarchical' | 'grid' | 'organic';
  };
}

interface FilterOptions {
  memoryTypes: string[];
  agents: string[];
  namespaces: string[];
  dateRange: { start: Date; end: Date };
  importanceRange: { min: number; max: number };
  tags: string[];
  searchQuery: string;
}

export const ClaudeFlowMemoryVisualization: React.FC<MemoryVisualizationProps> = ({
  memoryManager,
  integration
}) => {
  const [memories, setMemories] = useState<ClaudeFlowMemoryEntry[]>([]);
  const [nodes, setNodes] = useState<MemoryNode[]>([]);
  const [patterns, setPatterns] = useState<MemoryPattern[]>([]);
  const [selectedNode, setSelectedNode] = useState<MemoryNode | null>(null);
  const [selectedPattern, setSelectedPattern] = useState<MemoryPattern | null>(null);
  const [loading, setLoading] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [currentTimeFrame, setCurrentTimeFrame] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Visualization configuration
  const [visualMode, setVisualMode] = useState<VisualizationMode>({
    type: 'network',
    config: {
      showConnections: true,
      showLabels: true,
      showMetadata: false,
      colorBy: 'type',
      sizeBy: 'importance',
      layout: 'force'
    }
  });

  // Filters
  const [filters, setFilters] = useState<FilterOptions>({
    memoryTypes: [],
    agents: [],
    namespaces: [],
    dateRange: { 
      start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), 
      end: new Date() 
    },
    importanceRange: { min: 0, max: 1 },
    tags: [],
    searchQuery: ''
  });

  // Load and process memory data
  useEffect(() => {
    loadMemoryData();
  }, [filters]);

  // Auto-play timeline
  useEffect(() => {
    if (isPlaying && visualMode.type === 'timeline') {
      const interval = setInterval(() => {
        setCurrentTimeFrame(prev => {
          const maxFrames = Math.max(1, nodes.length / 10);
          return prev >= maxFrames ? 0 : prev + 1;
        });
      }, 1000 / playbackSpeed);
      return () => clearInterval(interval);
    }
  }, [isPlaying, playbackSpeed, nodes.length, visualMode.type]);

  /**
   * Load memory data and process for visualization
   */
  const loadMemoryData = async () => {
    setLoading(true);
    try {
      // Build query options from filters
      const queryOptions: MemoryQueryOptions = {
        limit: 1000,
        includeMetadata: true
      };

      // Apply filters
      if (filters.memoryTypes.length > 0) {
        queryOptions.filters = [
          { field: 'type', operator: 'in', value: filters.memoryTypes }
        ];
      }

      const memoryData = await memoryManager.queryMemories(queryOptions);
      setMemories(memoryData);

      // Transform memories into nodes
      const memoryNodes = await processMemoriesToNodes(memoryData);
      setNodes(memoryNodes);

      // Detect patterns
      const detectedPatterns = await detectMemoryPatterns(memoryData, memoryNodes);
      setPatterns(detectedPatterns);

    } catch (error) {
      console.error('Failed to load memory visualization data:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Transform memories into visualization nodes
   */
  const processMemoriesToNodes = async (memories: ClaudeFlowMemoryEntry[]): Promise<MemoryNode[]> => {
    return memories.map((memory, index) => ({
      id: memory.id,
      label: memory.content.substring(0, 50) + (memory.content.length > 50 ? '...' : ''),
      type: memory.type,
      agentId: memory.agentId,
      namespace: memory.namespace || 'default',
      size: memory.content.length,
      connections: extractConnections(memory, memories),
      position: generatePosition(index, memories.length),
      color: getTypeColor(memory.type),
      importance: calculateImportance(memory),
      accessCount: Math.random() * 20, // Mock - would come from database
      lastAccessed: memory.timestamp,
      tags: memory.tags,
      content: memory.content
    }));
  };

  /**
   * Extract connections between memories
   */
  const extractConnections = (memory: ClaudeFlowMemoryEntry, allMemories: ClaudeFlowMemoryEntry[]): string[] => {
    const connections: string[] = [];
    
    // Simple connection detection based on:
    // 1. Same agent
    // 2. Similar tags
    // 3. Similar content (could use embedding similarity)
    // 4. Temporal proximity
    
    const memoryTime = new Date(memory.timestamp).getTime();
    const timeWindow = 60 * 60 * 1000; // 1 hour
    
    allMemories.forEach(other => {
      if (other.id === memory.id) return;
      
      const otherTime = new Date(other.timestamp).getTime();
      const timeDiff = Math.abs(memoryTime - otherTime);
      
      // Connect if same agent and within time window
      if (memory.agentId === other.agentId && timeDiff < timeWindow) {
        connections.push(other.id);
      }
      
      // Connect if sharing tags
      const sharedTags = memory.tags.filter(tag => other.tags.includes(tag));
      if (sharedTags.length > 0) {
        connections.push(other.id);
      }
    });
    
    return [...new Set(connections)]; // Remove duplicates
  };

  /**
   * Generate node position based on layout algorithm
   */
  const generatePosition = (index: number, total: number): { x: number; y: number } => {
    switch (visualMode.config.layout) {
      case 'circular':
        const angle = (index / total) * 2 * Math.PI;
        const radius = 200;
        return {
          x: 400 + radius * Math.cos(angle),
          y: 300 + radius * Math.sin(angle)
        };
      
      case 'grid':
        const cols = Math.ceil(Math.sqrt(total));
        const gridSize = 100;
        return {
          x: (index % cols) * gridSize + 50,
          y: Math.floor(index / cols) * gridSize + 50
        };
      
      case 'force':
      default:
        // Simple force-directed layout (would use D3.js or similar in real implementation)
        return {
          x: Math.random() * 800 + 50,
          y: Math.random() * 600 + 50
        };
    }
  };

  /**
   * Get color for memory type
   */
  const getTypeColor = (type: string): string => {
    const colors = {
      observation: '#3B82F6', // Blue
      insight: '#F59E0B',     // Amber
      decision: '#8B5CF6',    // Violet
      artifact: '#10B981',    // Emerald
      error: '#EF4444',       // Red
      pattern: '#F97316',     // Orange
      knowledge: '#06B6D4'    // Cyan
    };
    return colors[type as keyof typeof colors] || '#6B7280';
  };

  /**
   * Calculate memory importance score
   */
  const calculateImportance = (memory: ClaudeFlowMemoryEntry): number => {
    // Mock importance calculation
    // In real implementation, this would consider:
    // - Access frequency
    // - Recency
    // - Connection count
    // - Tag importance
    // - Content analysis
    return Math.random();
  };

  /**
   * Detect patterns in memory data
   */
  const detectMemoryPatterns = async (
    memories: ClaudeFlowMemoryEntry[], 
    nodes: MemoryNode[]
  ): Promise<MemoryPattern[]> => {
    const detectedPatterns: MemoryPattern[] = [];

    // Temporal patterns - sequences of related memories
    const temporalPattern = detectTemporalPatterns(memories);
    if (temporalPattern.length > 0) {
      detectedPatterns.push({
        id: 'temporal-1',
        type: 'temporal',
        nodes: temporalPattern.map(m => m.id),
        strength: 0.8,
        frequency: 5,
        description: 'Sequential decision-making pattern detected',
        insights: [
          'Agent shows consistent problem-solving approach',
          'Decision sequence indicates learning progression'
        ]
      });
    }

    // Semantic patterns - similar content clusters
    const semanticClusters = detectSemanticClusters(memories);
    semanticClusters.forEach((cluster, index) => {
      detectedPatterns.push({
        id: `semantic-${index}`,
        type: 'semantic',
        nodes: cluster.map(m => m.id),
        strength: 0.6,
        frequency: cluster.length,
        description: `Content similarity cluster (${cluster.length} memories)`,
        insights: [`Common themes in ${cluster[0]?.type} memories`]
      });
    });

    // Structural patterns - connection patterns
    const structuralPatterns = detectStructuralPatterns(nodes);
    structuralPatterns.forEach((pattern, index) => {
      detectedPatterns.push({
        id: `structural-${index}`,
        type: 'structural',
        nodes: pattern.nodes,
        strength: pattern.strength,
        frequency: pattern.frequency,
        description: pattern.description,
        insights: pattern.insights
      });
    });

    return detectedPatterns;
  };

  /**
   * Detect temporal patterns in memories
   */
  const detectTemporalPatterns = (memories: ClaudeFlowMemoryEntry[]): ClaudeFlowMemoryEntry[] => {
    // Sort by timestamp
    const sorted = [...memories].sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    // Find sequences of related memories (same agent, similar time gaps)
    const sequences: ClaudeFlowMemoryEntry[][] = [];
    let currentSequence: ClaudeFlowMemoryEntry[] = [];

    for (let i = 0; i < sorted.length; i++) {
      const memory = sorted[i];
      const prevMemory = sorted[i - 1];

      if (prevMemory && 
          memory.agentId === prevMemory.agentId &&
          new Date(memory.timestamp).getTime() - new Date(prevMemory.timestamp).getTime() < 3600000) {
        // Within 1 hour of previous memory from same agent
        if (currentSequence.length === 0) {
          currentSequence.push(prevMemory);
        }
        currentSequence.push(memory);
      } else {
        if (currentSequence.length > 2) {
          sequences.push(currentSequence);
        }
        currentSequence = [];
      }
    }

    // Return the longest sequence
    return sequences.reduce((longest, current) => 
      current.length > longest.length ? current : longest, []
    );
  };

  /**
   * Detect semantic clusters
   */
  const detectSemanticClusters = (memories: ClaudeFlowMemoryEntry[]): ClaudeFlowMemoryEntry[][] => {
    // Simple clustering by shared keywords (in real implementation, use embeddings)
    const clusters = new Map<string, ClaudeFlowMemoryEntry[]>();

    memories.forEach(memory => {
      const words = memory.content.toLowerCase().split(/\s+/);
      const keywords = words.filter(word => word.length > 4); // Simple keyword extraction

      keywords.forEach(keyword => {
        if (!clusters.has(keyword)) {
          clusters.set(keyword, []);
        }
        clusters.get(keyword)!.push(memory);
      });
    });

    // Return clusters with more than 2 memories
    return Array.from(clusters.values()).filter(cluster => cluster.length > 2);
  };

  /**
   * Detect structural patterns
   */
  const detectStructuralPatterns = (nodes: MemoryNode[]): any[] => {
    const patterns = [];

    // Hub detection - nodes with many connections
    const hubs = nodes.filter(node => node.connections.length > 5);
    if (hubs.length > 0) {
      patterns.push({
        nodes: hubs.map(h => h.id),
        strength: 0.9,
        frequency: hubs.length,
        description: `Memory hub pattern (${hubs.length} central nodes)`,
        insights: ['Central memories serve as knowledge anchors', 'High connectivity indicates importance']
      });
    }

    // Isolation detection - nodes with few connections
    const isolated = nodes.filter(node => node.connections.length === 0);
    if (isolated.length > nodes.length * 0.2) {
      patterns.push({
        nodes: isolated.map(i => i.id),
        strength: 0.5,
        frequency: isolated.length,
        description: `Isolated memories detected (${isolated.length} nodes)`,
        insights: ['Some memories lack connections', 'Consider improving memory linking']
      });
    }

    return patterns;
  };

  /**
   * Filter nodes based on current filters
   */
  const filteredNodes = useMemo(() => {
    return nodes.filter(node => {
      // Type filter
      if (filters.memoryTypes.length > 0 && !filters.memoryTypes.includes(node.type)) {
        return false;
      }

      // Agent filter
      if (filters.agents.length > 0 && !filters.agents.includes(node.agentId)) {
        return false;
      }

      // Namespace filter
      if (filters.namespaces.length > 0 && !filters.namespaces.includes(node.namespace)) {
        return false;
      }

      // Search query
      if (filters.searchQuery && !node.content.toLowerCase().includes(filters.searchQuery.toLowerCase())) {
        return false;
      }

      // Importance range
      if (node.importance < filters.importanceRange.min || node.importance > filters.importanceRange.max) {
        return false;
      }

      return true;
    });
  }, [nodes, filters]);

  /**
   * Handle node selection
   */
  const handleNodeClick = useCallback((node: MemoryNode) => {
    setSelectedNode(node);
  }, []);

  /**
   * Handle pattern selection
   */
  const handlePatternClick = useCallback((pattern: MemoryPattern) => {
    setSelectedPattern(pattern);
  }, []);

  /**
   * Render network visualization
   */
  const renderNetworkVisualization = () => (
    <div className="relative w-full h-[600px] bg-background/50 rounded-lg border overflow-hidden">
      <svg className="w-full h-full">
        {/* Render connections */}
        {visualMode.config.showConnections && filteredNodes.map(node =>
          node.connections
            .filter(connId => filteredNodes.some(n => n.id === connId))
            .map(connId => {
              const targetNode = filteredNodes.find(n => n.id === connId);
              if (!targetNode) return null;
              
              return (
                <line
                  key={`${node.id}-${connId}`}
                  x1={node.position.x}
                  y1={node.position.y}
                  x2={targetNode.position.x}
                  y2={targetNode.position.y}
                  stroke="currentColor"
                  strokeOpacity={0.2}
                  strokeWidth={1}
                />
              );
            })
        )}

        {/* Render nodes */}
        {filteredNodes.map(node => {
          const nodeSize = visualMode.config.sizeBy === 'uniform' ? 8 : 
                          Math.max(4, Math.min(20, node[visualMode.config.sizeBy as keyof MemoryNode] as number * 20));
          
          return (
            <g key={node.id}>
              <circle
                cx={node.position.x}
                cy={node.position.y}
                r={nodeSize}
                fill={node.color}
                stroke="white"
                strokeWidth={selectedNode?.id === node.id ? 3 : 1}
                className="cursor-pointer hover:opacity-80 transition-opacity"
                onClick={() => handleNodeClick(node)}
              />
              {visualMode.config.showLabels && (
                <text
                  x={node.position.x}
                  y={node.position.y + nodeSize + 12}
                  textAnchor="middle"
                  className="text-xs fill-current opacity-70"
                  style={{ fontSize: '10px' }}
                >
                  {node.label}
                </text>
              )}
            </g>
          );
        })}
      </svg>

      {/* Overlay controls */}
      <div className="absolute top-4 right-4 flex gap-2">
        <Button size="sm" variant="outline" onClick={() => setIsFullscreen(!isFullscreen)}>
          {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
        </Button>
        <Button size="sm" variant="outline" onClick={() => setShowSettings(true)}>
          <Settings className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  /**
   * Render timeline visualization
   */
  const renderTimelineVisualization = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-center gap-4 p-4 bg-background/50 rounded-lg border">
        <Button
          size="sm"
          variant="outline"
          onClick={() => setIsPlaying(!isPlaying)}
        >
          {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
        </Button>
        
        <div className="flex items-center gap-2">
          <span className="text-sm">Speed:</span>
          <Select value={playbackSpeed.toString()} onValueChange={(v) => setPlaybackSpeed(Number(v))}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0.5">0.5x</SelectItem>
              <SelectItem value="1">1x</SelectItem>
              <SelectItem value="2">2x</SelectItem>
              <SelectItem value="4">4x</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button size="sm" variant="outline" onClick={() => setCurrentTimeFrame(0)}>
          <RotateCcw className="h-4 w-4" />
        </Button>
      </div>

      <div className="relative h-[400px] bg-background/50 rounded-lg border overflow-hidden">
        <div className="absolute inset-0 p-4">
          <div className="text-center text-muted-foreground">
            <Clock className="h-12 w-12 mx-auto mb-2" />
            <p>Timeline visualization</p>
            <p className="text-sm">Frame {currentTimeFrame + 1}</p>
          </div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <Card className="border-purple-500/20">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Brain className="h-12 w-12 animate-pulse text-purple-500 mx-auto mb-4" />
            <p className="text-muted-foreground">Loading memory visualization...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Network className="h-8 w-8 text-purple-500" />
          <div>
            <h2 className="text-2xl font-bold">Memory Visualization</h2>
            <p className="text-muted-foreground">
              Interactive exploration of memory patterns and connections
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Badge variant="outline">{filteredNodes.length} nodes</Badge>
          <Badge variant="outline">{patterns.length} patterns</Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar - Controls and Filters */}
        <div className="space-y-4">
          {/* Visualization Mode */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Visualization Mode</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Select 
                value={visualMode.type} 
                onValueChange={(value: any) => setVisualMode(prev => ({ ...prev, type: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="network">Network Graph</SelectItem>
                  <SelectItem value="timeline">Timeline</SelectItem>
                  <SelectItem value="hierarchy">Hierarchy</SelectItem>
                  <SelectItem value="heatmap">Heatmap</SelectItem>
                  <SelectItem value="flow">Flow Diagram</SelectItem>
                  <SelectItem value="3d">3D Visualization</SelectItem>
                </SelectContent>
              </Select>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Show Connections</span>
                  <Switch
                    checked={visualMode.config.showConnections}
                    onCheckedChange={(checked) => 
                      setVisualMode(prev => ({
                        ...prev,
                        config: { ...prev.config, showConnections: checked }
                      }))
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Show Labels</span>
                  <Switch
                    checked={visualMode.config.showLabels}
                    onCheckedChange={(checked) => 
                      setVisualMode(prev => ({
                        ...prev,
                        config: { ...prev.config, showLabels: checked }
                      }))
                    }
                  />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Color By</label>
                <Select 
                  value={visualMode.config.colorBy} 
                  onValueChange={(value: any) => 
                    setVisualMode(prev => ({
                      ...prev,
                      config: { ...prev.config, colorBy: value }
                    }))
                  }
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="type">Memory Type</SelectItem>
                    <SelectItem value="agent">Agent</SelectItem>
                    <SelectItem value="importance">Importance</SelectItem>
                    <SelectItem value="age">Age</SelectItem>
                    <SelectItem value="namespace">Namespace</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Size By</label>
                <Select 
                  value={visualMode.config.sizeBy} 
                  onValueChange={(value: any) => 
                    setVisualMode(prev => ({
                      ...prev,
                      config: { ...prev.config, sizeBy: value }
                    }))
                  }
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="importance">Importance</SelectItem>
                    <SelectItem value="size">Content Size</SelectItem>
                    <SelectItem value="access">Access Count</SelectItem>
                    <SelectItem value="uniform">Uniform</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Filters */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Filters</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Input
                  placeholder="Search memories..."
                  value={filters.searchQuery}
                  onChange={(e) => setFilters(prev => ({ ...prev, searchQuery: e.target.value }))}
                  className="text-sm"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Importance Range</label>
                <Slider
                  value={[filters.importanceRange.min * 100, filters.importanceRange.max * 100]}
                  onValueChange={([min, max]) => 
                    setFilters(prev => ({
                      ...prev,
                      importanceRange: { min: min / 100, max: max / 100 }
                    }))
                  }
                  min={0}
                  max={100}
                  step={1}
                  className="mt-2"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>{(filters.importanceRange.min * 100).toFixed(0)}%</span>
                  <span>{(filters.importanceRange.max * 100).toFixed(0)}%</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Detected Patterns */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Detected Patterns</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-40">
                <div className="space-y-2">
                  {patterns.map(pattern => (
                    <motion.div
                      key={pattern.id}
                      className={`p-2 rounded text-xs cursor-pointer transition-colors ${
                        selectedPattern?.id === pattern.id 
                          ? 'bg-purple-500/20 border border-purple-500/30' 
                          : 'bg-background/50 hover:bg-background/80'
                      }`}
                      onClick={() => handlePatternClick(pattern)}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-center gap-1 mb-1">
                        <Badge variant="secondary" className="text-xs px-1 py-0">
                          {pattern.type}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {pattern.strength.toFixed(2)}
                        </span>
                      </div>
                      <p className="text-xs">{pattern.description}</p>
                    </motion.div>
                  ))}
                  {patterns.length === 0 && (
                    <div className="text-center py-4 text-muted-foreground">
                      <Radar className="h-8 w-8 mx-auto mb-2" />
                      <p className="text-xs">No patterns detected</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        {/* Main Visualization Area */}
        <div className="lg:col-span-3 space-y-4">
          {/* Visualization */}
          {visualMode.type === 'network' && renderNetworkVisualization()}
          {visualMode.type === 'timeline' && renderTimelineVisualization()}
          {visualMode.type !== 'network' && visualMode.type !== 'timeline' && (
            <Card>
              <CardContent className="flex items-center justify-center h-[600px]">
                <div className="text-center text-muted-foreground">
                  <Map className="h-12 w-12 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">{visualMode.type} Visualization</h3>
                  <p>Coming soon in future updates</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Selection Details */}
          {(selectedNode || selectedPattern) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">
                  {selectedNode ? 'Memory Details' : 'Pattern Details'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {selectedNode && (
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-xs text-muted-foreground">Type</label>
                        <p className="text-sm font-medium">{selectedNode.type}</p>
                      </div>
                      <div>
                        <label className="text-xs text-muted-foreground">Agent</label>
                        <p className="text-sm font-medium">{selectedNode.agentId}</p>
                      </div>
                      <div>
                        <label className="text-xs text-muted-foreground">Namespace</label>
                        <p className="text-sm font-medium">{selectedNode.namespace}</p>
                      </div>
                      <div>
                        <label className="text-xs text-muted-foreground">Importance</label>
                        <p className="text-sm font-medium">{selectedNode.importance.toFixed(2)}</p>
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-xs text-muted-foreground">Content</label>
                      <p className="text-sm bg-background/50 p-2 rounded mt-1">
                        {selectedNode.content}
                      </p>
                    </div>
                    
                    {selectedNode.tags.length > 0 && (
                      <div>
                        <label className="text-xs text-muted-foreground">Tags</label>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {selectedNode.tags.map(tag => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {selectedPattern && (
                  <div className="space-y-3">
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <label className="text-xs text-muted-foreground">Type</label>
                        <p className="text-sm font-medium capitalize">{selectedPattern.type}</p>
                      </div>
                      <div>
                        <label className="text-xs text-muted-foreground">Strength</label>
                        <p className="text-sm font-medium">{selectedPattern.strength.toFixed(2)}</p>
                      </div>
                      <div>
                        <label className="text-xs text-muted-foreground">Frequency</label>
                        <p className="text-sm font-medium">{selectedPattern.frequency}</p>
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-xs text-muted-foreground">Description</label>
                      <p className="text-sm">{selectedPattern.description}</p>
                    </div>
                    
                    <div>
                      <label className="text-xs text-muted-foreground">Insights</label>
                      <ul className="text-sm space-y-1 mt-1">
                        {selectedPattern.insights.map((insight, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <Info className="h-3 w-3 text-muted-foreground flex-shrink-0 mt-0.5" />
                            <span>{insight}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Settings Dialog */}
      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Visualization Settings</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Layout Algorithm</label>
              <Select 
                value={visualMode.config.layout} 
                onValueChange={(value: any) => 
                  setVisualMode(prev => ({
                    ...prev,
                    config: { ...prev.config, layout: value }
                  }))
                }
              >
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="force">Force-Directed</SelectItem>
                  <SelectItem value="circular">Circular</SelectItem>
                  <SelectItem value="hierarchical">Hierarchical</SelectItem>
                  <SelectItem value="grid">Grid</SelectItem>
                  <SelectItem value="organic">Organic</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Show Metadata</span>
              <Switch
                checked={visualMode.config.showMetadata}
                onCheckedChange={(checked) => 
                  setVisualMode(prev => ({
                    ...prev,
                    config: { ...prev.config, showMetadata: checked }
                  }))
                }
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ClaudeFlowMemoryVisualization;