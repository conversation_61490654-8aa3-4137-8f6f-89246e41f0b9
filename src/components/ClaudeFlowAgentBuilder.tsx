import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ClaudeFlowErrorBoundary } from "./ClaudeFlowErrorBoundary";
import { useClaudeFlowError } from "@/hooks/useClaudeFlowError";
import { 
  ArrowLeft,
  Bot, 
  Brain,
  Zap,
  Shield,
  Settings,
  Code,
  TestTube,
  Search,
  Monitor,
  GitBranch,
  Network,
  Target,
  TrendingUp,
  Layers,
  Users,
  Plus,
  Trash2,
  Save,
  Copy,
  FileTemplate,
  Sparkles,
  Cpu,
  MemoryStick,
  HardDrive,
  Wifi,
  Check,
  X,
  AlertCircle,
  Info
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ClaudeFlowIntegrationV2 } from "@/lib/claudeFlowIntegrationV2";
import { useOptimizedMCP } from "@/contexts/OptimizedMCPContext";

interface ClaudeFlowAgentBuilderProps {
  onBack: () => void;
  className?: string;
}

interface AgentTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  type: string;
  capabilities: string[];
  systemPrompt: string;
  resources: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  securityLevel: 'low' | 'normal' | 'high';
  features: string[];
}

interface AgentConfig {
  name: string;
  description: string;
  type: string;
  capabilities: string[];
  systemPrompt: string;
  resources: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  securityLevel: 'low' | 'normal' | 'high';
  advanced: {
    maxConcurrentTasks: number;
    priority: number;
    autoScale: boolean;
    faultTolerance: boolean;
    encryption: boolean;
    auditLogging: boolean;
  };
}

const agentTemplates: AgentTemplate[] = [
  {
    id: 'specialist',
    name: 'Specialist Agent',
    description: 'Focused on specific domain expertise',
    icon: <Brain className="h-6 w-6" />,
    type: 'specialist',
    capabilities: ['analysis', 'problem-solving', 'domain-expertise'],
    systemPrompt: 'You are a specialist agent focused on deep domain expertise and analytical problem-solving.',
    resources: { cpu: 15, memory: 20, disk: 10, network: 10 },
    securityLevel: 'normal',
    features: ['Deep Analysis', 'Pattern Recognition', 'Expert Knowledge']
  },
  {
    id: 'coordinator',
    name: 'Coordinator Agent',
    description: 'Manages and orchestrates other agents',
    icon: <Network className="h-6 w-6" />,
    type: 'coordinator',
    capabilities: ['coordination', 'task-distribution', 'team-management'],
    systemPrompt: 'You are a coordinator agent responsible for managing and orchestrating other agents efficiently.',
    resources: { cpu: 10, memory: 15, disk: 5, network: 25 },
    securityLevel: 'high',
    features: ['Agent Orchestration', 'Task Distribution', 'Resource Management']
  },
  {
    id: 'analyzer',
    name: 'Analyzer Agent',
    description: 'Data analysis and pattern recognition',
    icon: <TrendingUp className="h-6 w-6" />,
    type: 'analyst',
    capabilities: ['data-analysis', 'pattern-recognition', 'reporting'],
    systemPrompt: 'You are an analyzer agent specialized in data analysis, pattern recognition, and insights generation.',
    resources: { cpu: 25, memory: 30, disk: 15, network: 10 },
    securityLevel: 'normal',
    features: ['Data Mining', 'Statistical Analysis', 'Trend Detection']
  },
  {
    id: 'security',
    name: 'Security Agent',
    description: 'Security monitoring and threat detection',
    icon: <Shield className="h-6 w-6" />,
    type: 'security',
    capabilities: ['security-monitoring', 'threat-detection', 'compliance'],
    systemPrompt: 'You are a security agent focused on threat detection, vulnerability assessment, and compliance monitoring.',
    resources: { cpu: 20, memory: 25, disk: 20, network: 15 },
    securityLevel: 'high',
    features: ['Threat Detection', 'Vulnerability Assessment', 'Compliance Monitoring']
  },
  {
    id: 'optimizer',
    name: 'Optimizer Agent',
    description: 'Performance and resource optimization',
    icon: <Zap className="h-6 w-6" />,
    type: 'optimizer',
    capabilities: ['performance-optimization', 'resource-management', 'efficiency'],
    systemPrompt: 'You are an optimizer agent specialized in performance tuning and resource optimization.',
    resources: { cpu: 15, memory: 20, disk: 10, network: 15 },
    securityLevel: 'normal',
    features: ['Performance Tuning', 'Resource Optimization', 'Efficiency Analysis']
  },
  {
    id: 'monitor',
    name: 'Monitor Agent',
    description: 'System and process monitoring',
    icon: <Monitor className="h-6 w-6" />,
    type: 'monitor',
    capabilities: ['system-monitoring', 'alerting', 'health-checks'],
    systemPrompt: 'You are a monitor agent responsible for system monitoring, alerting, and health checks.',
    resources: { cpu: 10, memory: 15, disk: 5, network: 20 },
    securityLevel: 'normal',
    features: ['Health Monitoring', 'Alert Management', 'Metric Collection']
  }
];

const availableCapabilities = [
  'analysis', 'problem-solving', 'domain-expertise', 'coordination', 'task-distribution',
  'team-management', 'data-analysis', 'pattern-recognition', 'reporting', 'security-monitoring',
  'threat-detection', 'compliance', 'performance-optimization', 'resource-management',
  'efficiency', 'system-monitoring', 'alerting', 'health-checks', 'code-review',
  'testing', 'deployment', 'documentation', 'research', 'communication'
];

export const ClaudeFlowAgentBuilder: React.FC<ClaudeFlowAgentBuilderProps> = ({ 
  onBack, 
  className = "" 
}) => {
  const { logError } = useClaudeFlowError();
  const { getClient } = useOptimizedMCP();
  
  // State management
  const [claudeFlow, setClaudeFlow] = useState<ClaudeFlowIntegrationV2 | null>(null);
  const [activeTab, setActiveTab] = useState("template");
  const [selectedTemplate, setSelectedTemplate] = useState<AgentTemplate | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  // Agent configuration state
  const [agentConfig, setAgentConfig] = useState<AgentConfig>({
    name: '',
    description: '',
    type: 'specialist',
    capabilities: [],
    systemPrompt: '',
    resources: { cpu: 10, memory: 15, disk: 5, network: 10 },
    securityLevel: 'normal',
    advanced: {
      maxConcurrentTasks: 5,
      priority: 5,
      autoScale: false,
      faultTolerance: true,
      encryption: true,
      auditLogging: false
    }
  });

  // Initialize Claude-Flow
  useEffect(() => {
    const initializeClaudeFlow = async () => {
      try {
        const mcpClient = getClient('claude-flow-v2');
        if (!mcpClient) {
          throw new Error('Claude-Flow v2.0.0 MCP client not available');
        }

        const integration = new ClaudeFlowIntegrationV2({
          host: 'localhost',
          port: 8765,
          autoStart: true,
          hiveMindEnabled: true,
          neuralEnabled: true
        });

        await integration.initialize(mcpClient);
        setClaudeFlow(integration);
      } catch (error) {
        logError('Failed to initialize Claude-Flow v2.0.0 Agent Builder', error as Error);
      }
    };

    initializeClaudeFlow();
  }, [getClient, logError]);

  // Apply template to configuration
  const applyTemplate = (template: AgentTemplate) => {
    setAgentConfig({
      name: template.name,
      description: template.description,
      type: template.type,
      capabilities: [...template.capabilities],
      systemPrompt: template.systemPrompt,
      resources: { ...template.resources },
      securityLevel: template.securityLevel,
      advanced: {
        maxConcurrentTasks: 5,
        priority: 5,
        autoScale: false,
        faultTolerance: true,
        encryption: template.securityLevel === 'high',
        auditLogging: template.securityLevel === 'high'
      }
    });
    setSelectedTemplate(template);
    setActiveTab("basic");
  };

  // Add capability
  const addCapability = (capability: string) => {
    if (!agentConfig.capabilities.includes(capability)) {
      setAgentConfig(prev => ({
        ...prev,
        capabilities: [...prev.capabilities, capability]
      }));
    }
  };

  // Remove capability
  const removeCapability = (capability: string) => {
    setAgentConfig(prev => ({
      ...prev,
      capabilities: prev.capabilities.filter(c => c !== capability)
    }));
  };

  // Update resource allocation
  const updateResource = (resource: string, value: number) => {
    setAgentConfig(prev => ({
      ...prev,
      resources: {
        ...prev.resources,
        [resource]: value
      }
    }));
  };

  // Validate configuration
  const validateConfig = (): string[] => {
    const errors: string[] = [];
    
    if (!agentConfig.name.trim()) {
      errors.push('Agent name is required');
    }
    
    if (!agentConfig.description.trim()) {
      errors.push('Agent description is required');
    }
    
    if (agentConfig.capabilities.length === 0) {
      errors.push('At least one capability is required');
    }
    
    if (!agentConfig.systemPrompt.trim()) {
      errors.push('System prompt is required');
    }

    const totalResources = Object.values(agentConfig.resources).reduce((sum, val) => sum + val, 0);
    if (totalResources > 100) {
      errors.push('Total resource allocation cannot exceed 100%');
    }
    
    return errors;
  };

  // Create agent
  const createAgent = async () => {
    if (!claudeFlow) {
      logError('Claude-Flow not initialized', new Error('Claude-Flow integration not available'));
      return;
    }

    const errors = validateConfig();
    if (errors.length > 0) {
      logError('Configuration validation failed', new Error(errors.join(', ')));
      return;
    }

    setIsCreating(true);
    try {
      const agent = await claudeFlow.createDynamicAgent({
        type: agentConfig.type,
        capabilities: agentConfig.capabilities,
        resources: {
          memory: agentConfig.resources.memory,
          compute: agentConfig.resources.cpu > 20 ? 'high' : 'normal',
          priority: agentConfig.advanced.priority
        },
        securityLevel: agentConfig.securityLevel
      });

      // Success - reset form
      resetForm();
      onBack();
    } catch (error) {
      logError('Failed to create agent', error as Error);
    } finally {
      setIsCreating(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setAgentConfig({
      name: '',
      description: '',
      type: 'specialist',
      capabilities: [],
      systemPrompt: '',
      resources: { cpu: 10, memory: 15, disk: 5, network: 10 },
      securityLevel: 'normal',
      advanced: {
        maxConcurrentTasks: 5,
        priority: 5,
        autoScale: false,
        faultTolerance: true,
        encryption: true,
        auditLogging: false
      }
    });
    setSelectedTemplate(null);
    setActiveTab("template");
  };

  // Calculate resource total
  const resourceTotal = Object.values(agentConfig.resources).reduce((sum, val) => sum + val, 0);

  return (
    <ClaudeFlowErrorBoundary>
      <div className={`flex flex-col h-full bg-background ${className}`}>
        {/* Header */}
        <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-4">
              <Button variant="ghost" onClick={onBack} size="sm">
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold flex items-center gap-2">
                  <Bot className="h-6 w-6 text-primary" />
                  Agent Builder
                </h1>
                <p className="text-sm text-muted-foreground">
                  Create custom dynamic agents with specialized capabilities
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={() => setShowPreview(true)} size="sm">
                <Search className="h-4 w-4 mr-2" />
                Preview
              </Button>
              <Button variant="outline" onClick={resetForm} size="sm">
                <X className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button onClick={createAgent} disabled={isCreating || validateConfig().length > 0} size="sm">
                {isCreating ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <Zap className="h-4 w-4 mr-2" />
                    </motion.div>
                    Creating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Create Agent
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Validation Errors */}
        {validateConfig().length > 0 && (
          <div className="mx-4 mt-4">
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle className="h-5 w-5 text-red-500" />
                  <span className="font-medium text-red-700">Configuration Issues</span>
                </div>
                <ul className="text-sm text-red-600 space-y-1">
                  {validateConfig().map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Resource Usage Warning */}
        {resourceTotal > 80 && (
          <div className="mx-4 mt-4">
            <Card className="border-yellow-200 bg-yellow-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-yellow-500" />
                  <span className="text-sm text-yellow-700">
                    High resource usage ({resourceTotal}%). Consider optimizing resource allocation.
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 p-4">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="template">Templates</TabsTrigger>
              <TabsTrigger value="basic">Basic Config</TabsTrigger>
              <TabsTrigger value="capabilities">Capabilities</TabsTrigger>
              <TabsTrigger value="resources">Resources</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            {/* Templates Tab */}
            <TabsContent value="template" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <AnimatePresence>
                  {agentTemplates.map((template) => (
                    <motion.div
                      key={template.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Card 
                        className={`cursor-pointer hover:shadow-lg transition-all ${
                          selectedTemplate?.id === template.id ? 'ring-2 ring-primary' : ''
                        }`}
                        onClick={() => applyTemplate(template)}
                      >
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              {template.icon}
                              <div>
                                <CardTitle className="text-lg">{template.name}</CardTitle>
                              </div>
                            </div>
                            {selectedTemplate?.id === template.id && (
                              <Check className="h-5 w-5 text-primary" />
                            )}
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <p className="text-sm text-muted-foreground">
                            {template.description}
                          </p>
                          <div>
                            <Label className="text-xs text-muted-foreground">Features</Label>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {template.features.map((feature) => (
                                <Badge key={feature} variant="outline" className="text-xs">
                                  {feature}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div>
                            <Label className="text-xs text-muted-foreground">Resources</Label>
                            <div className="text-xs text-muted-foreground mt-1">
                              CPU: {template.resources.cpu}% | Memory: {template.resources.memory}%
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            </TabsContent>

            {/* Basic Config Tab */}
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Basic Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="agent-name">Agent Name *</Label>
                      <Input
                        id="agent-name"
                        placeholder="Enter agent name"
                        value={agentConfig.name}
                        onChange={(e) => setAgentConfig(prev => ({ ...prev, name: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="agent-description">Description *</Label>
                      <Textarea
                        id="agent-description"
                        placeholder="Describe the agent's purpose and role"
                        value={agentConfig.description}
                        onChange={(e) => setAgentConfig(prev => ({ ...prev, description: e.target.value }))}
                        rows={3}
                      />
                    </div>
                    <div>
                      <Label htmlFor="agent-type">Agent Type</Label>
                      <Select
                        value={agentConfig.type}
                        onValueChange={(value) => setAgentConfig(prev => ({ ...prev, type: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="specialist">Specialist</SelectItem>
                          <SelectItem value="coordinator">Coordinator</SelectItem>
                          <SelectItem value="analyst">Analyst</SelectItem>
                          <SelectItem value="security">Security</SelectItem>
                          <SelectItem value="optimizer">Optimizer</SelectItem>
                          <SelectItem value="monitor">Monitor</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="security-level">Security Level</Label>
                      <Select
                        value={agentConfig.securityLevel}
                        onValueChange={(value: 'low' | 'normal' | 'high') => 
                          setAgentConfig(prev => ({ ...prev, securityLevel: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="normal">Normal</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>System Prompt</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Label htmlFor="system-prompt">Agent System Prompt *</Label>
                    <Textarea
                      id="system-prompt"
                      placeholder="Define the agent's behavior, role, and instructions"
                      value={agentConfig.systemPrompt}
                      onChange={(e) => setAgentConfig(prev => ({ ...prev, systemPrompt: e.target.value }))}
                      rows={8}
                      className="mt-2"
                    />
                    <p className="text-xs text-muted-foreground mt-2">
                      This prompt defines how the agent will behave and respond to tasks.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Capabilities Tab */}
            <TabsContent value="capabilities" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Available Capabilities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-2">
                      {availableCapabilities
                        .filter(cap => !agentConfig.capabilities.includes(cap))
                        .map((capability) => (
                        <Button
                          key={capability}
                          variant="outline"
                          size="sm"
                          onClick={() => addCapability(capability)}
                          className="justify-start text-xs"
                        >
                          <Plus className="h-3 w-3 mr-2" />
                          {capability}
                        </Button>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Selected Capabilities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {agentConfig.capabilities.length === 0 ? (
                      <p className="text-sm text-muted-foreground">
                        No capabilities selected. Choose at least one capability.
                      </p>
                    ) : (
                      <div className="space-y-2">
                        {agentConfig.capabilities.map((capability) => (
                          <div
                            key={capability}
                            className="flex items-center justify-between p-2 bg-secondary rounded"
                          >
                            <span className="text-sm">{capability}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeCapability(capability)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Resources Tab */}
            <TabsContent value="resources" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Resource Allocation
                  </CardTitle>
                  <div className="flex items-center gap-2 text-sm">
                    <span className="text-muted-foreground">Total Usage:</span>
                    <span className={resourceTotal > 100 ? 'text-red-500' : 'text-primary'}>
                      {resourceTotal}%
                    </span>
                    <span className="text-muted-foreground">/ 100%</span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {Object.entries(agentConfig.resources).map(([resource, value]) => {
                      const icons = {
                        cpu: <Cpu className="h-5 w-5" />,
                        memory: <MemoryStick className="h-5 w-5" />,
                        disk: <HardDrive className="h-5 w-5" />,
                        network: <Wifi className="h-5 w-5" />
                      };

                      return (
                        <div key={resource} className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              {icons[resource as keyof typeof icons]}
                              <Label className="capitalize">{resource}</Label>
                            </div>
                            <span className="text-sm font-medium">{value}%</span>
                          </div>
                          <Slider
                            value={[value]}
                            onValueChange={([newValue]) => updateResource(resource, newValue)}
                            max={50}
                            min={5}
                            step={5}
                            className="w-full"
                          />
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Advanced Tab */}
            <TabsContent value="advanced" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="max-tasks">Max Concurrent Tasks</Label>
                      <div className="flex items-center gap-4 mt-2">
                        <Slider
                          value={[agentConfig.advanced.maxConcurrentTasks]}
                          onValueChange={([value]) => 
                            setAgentConfig(prev => ({
                              ...prev,
                              advanced: { ...prev.advanced, maxConcurrentTasks: value }
                            }))
                          }
                          max={20}
                          min={1}
                          step={1}
                          className="flex-1"
                        />
                        <span className="text-sm font-medium w-8">
                          {agentConfig.advanced.maxConcurrentTasks}
                        </span>
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="priority">Agent Priority</Label>
                      <div className="flex items-center gap-4 mt-2">
                        <Slider
                          value={[agentConfig.advanced.priority]}
                          onValueChange={([value]) => 
                            setAgentConfig(prev => ({
                              ...prev,
                              advanced: { ...prev.advanced, priority: value }
                            }))
                          }
                          max={10}
                          min={1}
                          step={1}
                          className="flex-1"
                        />
                        <span className="text-sm font-medium w-8">
                          {agentConfig.advanced.priority}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Features</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Auto Scaling</Label>
                        <p className="text-sm text-muted-foreground">
                          Automatically adjust resources based on demand
                        </p>
                      </div>
                      <Switch
                        checked={agentConfig.advanced.autoScale}
                        onCheckedChange={(checked) => 
                          setAgentConfig(prev => ({
                            ...prev,
                            advanced: { ...prev.advanced, autoScale: checked }
                          }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Fault Tolerance</Label>
                        <p className="text-sm text-muted-foreground">
                          Enable automatic recovery from failures
                        </p>
                      </div>
                      <Switch
                        checked={agentConfig.advanced.faultTolerance}
                        onCheckedChange={(checked) => 
                          setAgentConfig(prev => ({
                            ...prev,
                            advanced: { ...prev.advanced, faultTolerance: checked }
                          }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Encryption</Label>
                        <p className="text-sm text-muted-foreground">
                          Encrypt agent communications and data
                        </p>
                      </div>
                      <Switch
                        checked={agentConfig.advanced.encryption}
                        onCheckedChange={(checked) => 
                          setAgentConfig(prev => ({
                            ...prev,
                            advanced: { ...prev.advanced, encryption: checked }
                          }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Audit Logging</Label>
                        <p className="text-sm text-muted-foreground">
                          Log all agent activities for compliance
                        </p>
                      </div>
                      <Switch
                        checked={agentConfig.advanced.auditLogging}
                        onCheckedChange={(checked) => 
                          setAgentConfig(prev => ({
                            ...prev,
                            advanced: { ...prev.advanced, auditLogging: checked }
                          }))
                        }
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Preview Dialog */}
        <Dialog open={showPreview} onOpenChange={setShowPreview}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Agent Configuration Preview</DialogTitle>
              <DialogDescription>
                Review your agent configuration before creation
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              <div>
                <Label className="font-semibold">Basic Information</Label>
                <div className="mt-2 p-3 bg-secondary rounded">
                  <p><strong>Name:</strong> {agentConfig.name || 'Unnamed Agent'}</p>
                  <p><strong>Type:</strong> {agentConfig.type}</p>
                  <p><strong>Security Level:</strong> {agentConfig.securityLevel}</p>
                </div>
              </div>
              <div>
                <Label className="font-semibold">Description</Label>
                <p className="mt-2 p-3 bg-secondary rounded text-sm">
                  {agentConfig.description || 'No description provided'}
                </p>
              </div>
              <div>
                <Label className="font-semibold">Capabilities</Label>
                <div className="mt-2 flex flex-wrap gap-1">
                  {agentConfig.capabilities.map((cap) => (
                    <Badge key={cap} variant="outline">{cap}</Badge>
                  ))}
                </div>
              </div>
              <div>
                <Label className="font-semibold">Resource Allocation</Label>
                <div className="mt-2 p-3 bg-secondary rounded text-sm">
                  <p>CPU: {agentConfig.resources.cpu}%</p>
                  <p>Memory: {agentConfig.resources.memory}%</p>
                  <p>Disk: {agentConfig.resources.disk}%</p>
                  <p>Network: {agentConfig.resources.network}%</p>
                  <p><strong>Total: {resourceTotal}%</strong></p>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowPreview(false)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </ClaudeFlowErrorBoundary>
  );
};