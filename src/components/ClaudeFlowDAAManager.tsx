import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ClaudeFlowErrorBoundary } from "./ClaudeFlowErrorBoundary";
import { useClaudeFlowError } from "@/hooks/useClaudeFlowError";
import { 
  Network, 
  Bot, 
  Zap, 
  Play, 
  Pause, 
  Square, 
  Plus, 
  Trash2, 
  Activity, 
  Brain, 
  Settings, 
  Monitor,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2,
  RefreshCw,
  GitBranch,
  Cpu,
  MemoryStick,
  HardDrive,
  Wifi,
  Users,
  Target,
  TrendingUp,
  Clock,
  Layers,
  Sparkles
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Progress } from "@/components/ui/progress";
import { ClaudeFlowIntegrationV2, ClaudeFlowAgent } from "@/lib/claudeFlowIntegrationV2";
import { useOptimizedMCP } from "@/contexts/OptimizedMCPContext";

interface ClaudeFlowDAAManagerProps {
  onBack: () => void;
  className?: string;
}

interface DynamicAgent {
  id: string;
  name: string;
  type: string;
  status: 'idle' | 'active' | 'busy' | 'error' | 'terminated';
  capabilities: string[];
  resources: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  utilization: {
    cpu: number;
    memory: number;
    tasks: number;
  };
  health: number;
  createdAt: string;
  lastActive: string;
  parentId?: string;
  children: string[];
  metrics: {
    tasksCompleted: number;
    uptime: number;
    errorRate: number;
    efficiency: number;
  };
}

interface ResourcePool {
  total: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  allocated: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  available: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
}

const agentTypeIcons = {
  'queen': <Bot className="h-5 w-5" />,
  'architect': <Layers className="h-5 w-5" />,
  'coder': <Brain className="h-5 w-5" />,
  'tester': <CheckCircle className="h-5 w-5" />,
  'analyst': <TrendingUp className="h-5 w-5" />,
  'researcher': <Target className="h-5 w-5" />,
  'security': <Shield className="h-5 w-5" />,
  'devops': <Settings className="h-5 w-5" />,
  'coordinator': <Network className="h-5 w-5" />,
  'specialist': <Sparkles className="h-5 w-5" />,
  'monitor': <Monitor className="h-5 w-5" />,
  'optimizer': <Zap className="h-5 w-5" />
};

const statusColors = {
  'idle': 'bg-gray-500',
  'active': 'bg-green-500',
  'busy': 'bg-blue-500',
  'error': 'bg-red-500',
  'terminated': 'bg-gray-800'
};

export const ClaudeFlowDAAManager: React.FC<ClaudeFlowDAAManagerProps> = ({ 
  onBack, 
  className = "" 
}) => {
  const { logError, clearError } = useClaudeFlowError();
  const { getClient } = useOptimizedMCP();
  
  // State management
  const [claudeFlow, setClaudeFlow] = useState<ClaudeFlowIntegrationV2 | null>(null);
  const [agents, setAgents] = useState<DynamicAgent[]>([]);
  const [resourcePool, setResourcePool] = useState<ResourcePool>({
    total: { cpu: 100, memory: 100, disk: 100, network: 100 },
    allocated: { cpu: 0, memory: 0, disk: 0, network: 0 },
    available: { cpu: 100, memory: 100, disk: 100, network: 100 }
  });
  const [loading, setLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<DynamicAgent | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showResourceDialog, setShowResourceDialog] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  // Create agent form state
  const [newAgent, setNewAgent] = useState({
    name: '',
    type: 'specialist',
    capabilities: [] as string[],
    resources: { cpu: 10, memory: 10, disk: 10, network: 10 },
    securityLevel: 'normal' as 'low' | 'normal' | 'high'
  });

  // Initialize Claude-Flow v2.0.0 integration
  const initializeClaudeFlow = useCallback(async () => {
    try {
      const mcpClient = getClient('claude-flow-v2');
      if (!mcpClient) {
        throw new Error('Claude-Flow v2.0.0 MCP client not available');
      }

      const integration = new ClaudeFlowIntegrationV2({
        host: 'localhost',
        port: 8765,
        autoStart: true,
        hiveMindEnabled: true,
        neuralEnabled: true
      });

      await integration.initialize(mcpClient);
      setClaudeFlow(integration);
      await loadAgents(integration);
      await loadResourcePool(integration);
    } catch (error) {
      logError('Failed to initialize Claude-Flow v2.0.0 DAA', error as Error);
    } finally {
      setLoading(false);
    }
  }, [getClient, logError]);

  // Load dynamic agents
  const loadAgents = async (cf: ClaudeFlowIntegrationV2) => {
    try {
      const mcpClient = getClient('claude-flow-v2');
      if (!mcpClient) return;

      const response = await mcpClient.invoke('daa/agent-list', {});
      if (response.success) {
        setAgents(response.result.agents || []);
      }
    } catch (error) {
      logError('Failed to load dynamic agents', error as Error);
    }
  };

  // Load resource pool status
  const loadResourcePool = async (cf: ClaudeFlowIntegrationV2) => {
    try {
      const stats = await cf.getResourceStatistics();
      if (stats) {
        setResourcePool(stats);
      }
    } catch (error) {
      logError('Failed to load resource pool', error as Error);
    }
  };

  // Create dynamic agent
  const createAgent = async () => {
    if (!claudeFlow || !newAgent.name.trim()) return;

    try {
      const agent = await claudeFlow.createDynamicAgent({
        type: newAgent.type,
        capabilities: newAgent.capabilities,
        resources: {
          memory: newAgent.resources.memory,
          compute: 'normal',
          priority: 5
        },
        securityLevel: newAgent.securityLevel
      });

      await loadAgents(claudeFlow);
      await loadResourcePool(claudeFlow);
      setShowCreateDialog(false);
      resetNewAgentForm();
    } catch (error) {
      logError('Failed to create dynamic agent', error as Error);
    }
  };

  // Terminate agent
  const terminateAgent = async (agentId: string) => {
    if (!claudeFlow) return;

    try {
      const mcpClient = getClient('claude-flow-v2');
      if (!mcpClient) return;

      const response = await mcpClient.invoke('daa/lifecycle-manage', {
        agentId,
        action: 'terminate'
      });

      if (response.success) {
        await loadAgents(claudeFlow);
        await loadResourcePool(claudeFlow);
        setSelectedAgent(null);
      }
    } catch (error) {
      logError('Failed to terminate agent', error as Error);
    }
  };

  // Pause/Resume agent
  const toggleAgent = async (agentId: string, action: 'pause' | 'resume') => {
    if (!claudeFlow) return;

    try {
      const mcpClient = getClient('claude-flow-v2');
      if (!mcpClient) return;

      const response = await mcpClient.invoke('daa/lifecycle-manage', {
        agentId,
        action
      });

      if (response.success) {
        await loadAgents(claudeFlow);
      }
    } catch (error) {
      logError(`Failed to ${action} agent`, error as Error);
    }
  };

  // Resource optimization
  const optimizeResources = async () => {
    if (!claudeFlow) return;

    try {
      const mcpClient = getClient('claude-flow-v2');
      if (!mcpClient) return;

      const response = await mcpClient.invoke('daa/resource-alloc', {
        action: 'optimize'
      });

      if (response.success) {
        await loadResourcePool(claudeFlow);
      }
    } catch (error) {
      logError('Failed to optimize resources', error as Error);
    }
  };

  // Refresh data
  const refreshData = async () => {
    if (!claudeFlow) return;

    setIsRefreshing(true);
    try {
      await Promise.all([
        loadAgents(claudeFlow),
        loadResourcePool(claudeFlow)
      ]);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Reset new agent form
  const resetNewAgentForm = () => {
    setNewAgent({
      name: '',
      type: 'specialist',
      capabilities: [],
      resources: { cpu: 10, memory: 10, disk: 10, network: 10 },
      securityLevel: 'normal'
    });
  };

  // Get health color
  const getHealthColor = (health: number) => {
    if (health >= 80) return 'text-green-500';
    if (health >= 60) return 'text-yellow-500';
    if (health >= 40) return 'text-orange-500';
    return 'text-red-500';
  };

  // Calculate resource utilization percentage
  const getResourceUtilization = (allocated: number, total: number) => {
    return Math.round((allocated / total) * 100);
  };

  // Initialize on mount
  useEffect(() => {
    initializeClaudeFlow();
  }, [initializeClaudeFlow]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <Loader2 className="h-12 w-12 text-primary" />
        </motion.div>
      </div>
    );
  }

  return (
    <ClaudeFlowErrorBoundary>
      <div className={`flex flex-col h-full bg-background ${className}`}>
        {/* Header */}
        <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-4">
              <Button variant="ghost" onClick={onBack} size="sm">
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold flex items-center gap-2">
                  <Network className="h-6 w-6 text-primary" />
                  Dynamic Agent Architecture
                </h1>
                <p className="text-sm text-muted-foreground">
                  Manage self-organizing agents with fault tolerance
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                onClick={refreshData} 
                disabled={isRefreshing}
                size="sm"
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button onClick={() => setShowCreateDialog(true)} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Agent
              </Button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-4 space-y-6">
          {/* Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Active Agents</p>
                    <p className="text-2xl font-bold">
                      {agents.filter(a => a.status === 'active').length}
                    </p>
                  </div>
                  <Bot className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Agents</p>
                    <p className="text-2xl font-bold">{agents.length}</p>
                  </div>
                  <Users className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">CPU Usage</p>
                    <p className="text-2xl font-bold">
                      {getResourceUtilization(resourcePool.allocated.cpu, resourcePool.total.cpu)}%
                    </p>
                  </div>
                  <Cpu className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Memory Usage</p>
                    <p className="text-2xl font-bold">
                      {getResourceUtilization(resourcePool.allocated.memory, resourcePool.total.memory)}%
                    </p>
                  </div>
                  <MemoryStick className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="agents">Agents</TabsTrigger>
              <TabsTrigger value="resources">Resources</TabsTrigger>
              <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Agent Types Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <GitBranch className="h-5 w-5" />
                      Agent Distribution
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {Object.entries(
                        agents.reduce((acc, agent) => {
                          acc[agent.type] = (acc[agent.type] || 0) + 1;
                          return acc;
                        }, {} as Record<string, number>)
                      ).map(([type, count]) => (
                        <div key={type} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {agentTypeIcons[type as keyof typeof agentTypeIcons] || <Bot className="h-4 w-4" />}
                            <span className="capitalize">{type}</span>
                          </div>
                          <Badge variant="secondary">{count}</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Resource Pool Status */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      Resource Pool
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>CPU</span>
                        <span>{getResourceUtilization(resourcePool.allocated.cpu, resourcePool.total.cpu)}%</span>
                      </div>
                      <Progress value={getResourceUtilization(resourcePool.allocated.cpu, resourcePool.total.cpu)} />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Memory</span>
                        <span>{getResourceUtilization(resourcePool.allocated.memory, resourcePool.total.memory)}%</span>
                      </div>
                      <Progress value={getResourceUtilization(resourcePool.allocated.memory, resourcePool.total.memory)} />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Disk</span>
                        <span>{getResourceUtilization(resourcePool.allocated.disk, resourcePool.total.disk)}%</span>
                      </div>
                      <Progress value={getResourceUtilization(resourcePool.allocated.disk, resourcePool.total.disk)} />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Network</span>
                        <span>{getResourceUtilization(resourcePool.allocated.network, resourcePool.total.network)}%</span>
                      </div>
                      <Progress value={getResourceUtilization(resourcePool.allocated.network, resourcePool.total.network)} />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Agents Tab */}
            <TabsContent value="agents" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                <AnimatePresence>
                  {agents.map((agent) => (
                    <motion.div
                      key={agent.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Card className="cursor-pointer hover:shadow-lg transition-shadow" 
                            onClick={() => setSelectedAgent(agent)}>
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              {agentTypeIcons[agent.type as keyof typeof agentTypeIcons] || <Bot className="h-4 w-4" />}
                              <span className="font-medium">{agent.name}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <div className={`w-2 h-2 rounded-full ${statusColors[agent.status]}`} />
                              <span className="text-xs text-muted-foreground capitalize">
                                {agent.status}
                              </span>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div className="flex items-center justify-between text-sm">
                            <span>Health</span>
                            <span className={getHealthColor(agent.health)}>
                              {agent.health}%
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span>Tasks</span>
                            <span>{agent.metrics.tasksCompleted}</span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span>Efficiency</span>
                            <span>{Math.round(agent.metrics.efficiency)}%</span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {agent.capabilities.slice(0, 3).map((capability) => (
                              <Badge key={capability} variant="outline" className="text-xs">
                                {capability}
                              </Badge>
                            ))}
                            {agent.capabilities.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{agent.capabilities.length - 3}
                              </Badge>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            </TabsContent>

            {/* Resources Tab */}
            <TabsContent value="resources" className="space-y-4">
              <div className="flex justify-end">
                <Button onClick={optimizeResources} variant="outline">
                  <Zap className="h-4 w-4 mr-2" />
                  Optimize Resources
                </Button>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Resource Allocation</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {Object.entries(resourcePool.allocated).map(([resource, value]) => (
                      <div key={resource}>
                        <div className="flex justify-between text-sm mb-2">
                          <span className="capitalize">{resource}</span>
                          <span>{value} / {resourcePool.total[resource as keyof typeof resourcePool.total]}</span>
                        </div>
                        <Progress 
                          value={getResourceUtilization(value, resourcePool.total[resource as keyof typeof resourcePool.total])} 
                        />
                      </div>
                    ))}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Available Resources</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {Object.entries(resourcePool.available).map(([resource, value]) => (
                      <div key={resource}>
                        <div className="flex justify-between text-sm mb-2">
                          <span className="capitalize">{resource}</span>
                          <span>{value} available</span>
                        </div>
                        <Progress 
                          value={getResourceUtilization(value, resourcePool.total[resource as keyof typeof resourcePool.total])} 
                          className="bg-green-100"
                        />
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Monitoring Tab */}
            <TabsContent value="monitoring" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-green-600">Healthy Agents</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl font-bold">
                      {agents.filter(a => a.health >= 80).length}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-yellow-600">Warning Agents</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl font-bold">
                      {agents.filter(a => a.health >= 40 && a.health < 80).length}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-red-600">Critical Agents</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl font-bold">
                      {agents.filter(a => a.health < 40).length}
                    </p>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Agent Health Monitor</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {agents.map((agent) => (
                      <div key={agent.id} className="flex items-center justify-between p-2 border rounded">
                        <div className="flex items-center gap-2">
                          {agentTypeIcons[agent.type as keyof typeof agentTypeIcons] || <Bot className="h-4 w-4" />}
                          <span>{agent.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Progress value={agent.health} className="w-20" />
                          <span className={`text-sm ${getHealthColor(agent.health)}`}>
                            {agent.health}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Create Agent Dialog */}
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Create Dynamic Agent</DialogTitle>
              <DialogDescription>
                Create a new self-organizing agent with custom capabilities
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="agent-name">Agent Name</Label>
                <Input
                  id="agent-name"
                  placeholder="Enter agent name"
                  value={newAgent.name}
                  onChange={(e) => setNewAgent(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="agent-type">Agent Type</Label>
                <Select
                  value={newAgent.type}
                  onValueChange={(value) => setNewAgent(prev => ({ ...prev, type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="specialist">Specialist</SelectItem>
                    <SelectItem value="coordinator">Coordinator</SelectItem>
                    <SelectItem value="analyzer">Analyzer</SelectItem>
                    <SelectItem value="optimizer">Optimizer</SelectItem>
                    <SelectItem value="monitor">Monitor</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Security Level</Label>
                <Select
                  value={newAgent.securityLevel}
                  onValueChange={(value: 'low' | 'normal' | 'high') => 
                    setNewAgent(prev => ({ ...prev, securityLevel: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="normal">Normal</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Resource Allocation</Label>
                <div className="space-y-2">
                  <div>
                    <div className="flex justify-between text-sm">
                      <span>CPU: {newAgent.resources.cpu}%</span>
                    </div>
                    <Slider
                      value={[newAgent.resources.cpu]}
                      onValueChange={([value]) => 
                        setNewAgent(prev => ({ 
                          ...prev, 
                          resources: { ...prev.resources, cpu: value }
                        }))
                      }
                      max={50}
                      min={5}
                      step={5}
                    />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm">
                      <span>Memory: {newAgent.resources.memory}%</span>
                    </div>
                    <Slider
                      value={[newAgent.resources.memory]}
                      onValueChange={([value]) => 
                        setNewAgent(prev => ({ 
                          ...prev, 
                          resources: { ...prev.resources, memory: value }
                        }))
                      }
                      max={50}
                      min={5}
                      step={5}
                    />
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                Cancel
              </Button>
              <Button onClick={createAgent} disabled={!newAgent.name.trim()}>
                Create Agent
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Agent Details Dialog */}
        <Dialog open={!!selectedAgent} onOpenChange={() => setSelectedAgent(null)}>
          <DialogContent className="max-w-2xl">
            {selectedAgent && (
              <>
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    {agentTypeIcons[selectedAgent.type as keyof typeof agentTypeIcons] || <Bot className="h-5 w-5" />}
                    {selectedAgent.name}
                  </DialogTitle>
                  <DialogDescription>
                    Dynamic agent details and controls
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm text-muted-foreground">Status</Label>
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${statusColors[selectedAgent.status]}`} />
                        <span className="capitalize">{selectedAgent.status}</span>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm text-muted-foreground">Health</Label>
                      <span className={getHealthColor(selectedAgent.health)}>
                        {selectedAgent.health}%
                      </span>
                    </div>
                    <div>
                      <Label className="text-sm text-muted-foreground">Tasks Completed</Label>
                      <span>{selectedAgent.metrics.tasksCompleted}</span>
                    </div>
                    <div>
                      <Label className="text-sm text-muted-foreground">Efficiency</Label>
                      <span>{Math.round(selectedAgent.metrics.efficiency)}%</span>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm text-muted-foreground">Capabilities</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {selectedAgent.capabilities.map((capability) => (
                        <Badge key={capability} variant="outline">
                          {capability}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm text-muted-foreground">Resource Usage</Label>
                    <div className="space-y-2 mt-2">
                      <div>
                        <div className="flex justify-between text-sm">
                          <span>CPU</span>
                          <span>{selectedAgent.utilization.cpu}%</span>
                        </div>
                        <Progress value={selectedAgent.utilization.cpu} />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm">
                          <span>Memory</span>
                          <span>{selectedAgent.utilization.memory}%</span>
                        </div>
                        <Progress value={selectedAgent.utilization.memory} />
                      </div>
                    </div>
                  </div>
                </div>
                <DialogFooter className="gap-2">
                  <Button
                    variant="outline"
                    onClick={() => toggleAgent(selectedAgent.id, 
                      selectedAgent.status === 'active' ? 'pause' : 'resume'
                    )}
                    disabled={selectedAgent.status === 'terminated'}
                  >
                    {selectedAgent.status === 'active' ? (
                      <>
                        <Pause className="h-4 w-4 mr-2" />
                        Pause
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Resume
                      </>
                    )}
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => terminateAgent(selectedAgent.id)}
                    disabled={selectedAgent.status === 'terminated'}
                  >
                    <Square className="h-4 w-4 mr-2" />
                    Terminate
                  </Button>
                </DialogFooter>
              </>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </ClaudeFlowErrorBoundary>
  );
};