/**
 * Claude-Flow v2.0.0 Memory Management Dashboard
 * Enterprise-grade memory visualization and analytics
 */

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain,
  Database,
  BarChart3,
  PieChart,
  Zap,
  HardDrive,
  Archive,
  Shield,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Layers,
  Network,
  Cpu,
  Activity,
  Filter,
  Search,
  Download,
  Upload,
  Settings,
  RefreshCw,
  Maximize2,
  MoreHorizontal,
  Trash2,
  Lock
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ClaudeFlowMemoryManager } from '@/lib/claudeFlowMemoryManager';
import { ClaudeFlowIntegrationV2 } from '@/lib/claudeFlowIntegrationV2';

interface MemoryDashboardProps {
  memoryManager: ClaudeFlowMemoryManager;
  integration: ClaudeFlowIntegrationV2;
}

interface MemoryStats {
  totalEntries: number;
  totalSize: number;
  byType: Record<string, number>;
  byNamespace: Record<string, number>;
  byAgent: Record<string, number>;
  compressionRatio: number;
  encryptedEntries: number;
  oldestEntry: string | null;
  newestEntry: string | null;
  avgAccessFrequency: number;
  hotMemories: number;
  coldMemories: number;
}

interface PerformanceMetrics {
  queryLatency: number;
  writeLatency: number;
  readThroughput: number;
  writeThroughput: number;
  cacheHitRate: number;
  indexEfficiency: number;
  compressionPerformance: number;
  encryptionOverhead: number;
}

interface MemoryHealth {
  overallScore: number;
  indicators: {
    performance: number;
    storage: number;
    integrity: number;
    security: number;
    optimization: number;
  };
  issues: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
    resolution?: string;
  }>;
  recommendations: string[];
}

export const ClaudeFlowMemoryDashboard: React.FC<MemoryDashboardProps> = ({
  memoryManager,
  integration
}) => {
  const [stats, setStats] = useState<MemoryStats | null>(null);
  const [performance, setPerformance] = useState<PerformanceMetrics | null>(null);
  const [health, setHealth] = useState<MemoryHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5000);
  const [selectedTimeRange, setSelectedTimeRange] = useState('1h');
  const [showSettings, setShowSettings] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [realTimeMode, setRealTimeMode] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout>();

  // Real-time data updates
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(loadDashboardData, refreshInterval);
      intervalRef.current = interval;
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  useEffect(() => {
    loadDashboardData();
  }, [selectedTimeRange]);

  /**
   * Load comprehensive dashboard data
   */
  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load memory statistics
      const memoryStats = await memoryManager.getMemoryStats();
      const dbInfo = memoryManager.getDatabaseInfo();

      // Calculate enhanced statistics
      const enhancedStats: MemoryStats = {
        ...memoryStats,
        compressionRatio: calculateCompressionRatio(memoryStats),
        encryptedEntries: calculateEncryptedEntries(memoryStats),
        avgAccessFrequency: calculateAccessFrequency(memoryStats),
        hotMemories: calculateHotMemories(memoryStats),
        coldMemories: calculateColdMemories(memoryStats)
      };

      setStats(enhancedStats);

      // Load performance metrics from integration
      const perfReport = await integration.getPerformanceReport(selectedTimeRange as any);
      setPerformance(transformPerformanceData(perfReport));

      // Calculate health indicators
      setHealth(calculateHealthIndicators(enhancedStats, perfReport, dbInfo));

    } catch (error) {
      console.error('Failed to load memory dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Calculate compression ratio
   */
  const calculateCompressionRatio = (stats: any): number => {
    // Mock calculation - in real implementation, this would analyze actual compression
    return Math.random() * 0.4 + 0.3; // 30-70% compression
  };

  /**
   * Calculate encrypted entries
   */
  const calculateEncryptedEntries = (stats: any): number => {
    return Math.floor(stats.totalEntries * (Math.random() * 0.5 + 0.5)); // 50-100% encrypted
  };

  /**
   * Calculate access frequency
   */
  const calculateAccessFrequency = (stats: any): number => {
    return Math.random() * 10 + 1; // 1-11 accesses per entry on average
  };

  /**
   * Calculate hot/cold memory distribution
   */
  const calculateHotMemories = (stats: any): number => {
    return Math.floor(stats.totalEntries * 0.2); // 20% are hot
  };

  const calculateColdMemories = (stats: any): number => {
    return Math.floor(stats.totalEntries * 0.3); // 30% are cold
  };

  /**
   * Transform performance data from integration
   */
  const transformPerformanceData = (perfReport: any): PerformanceMetrics => {
    return {
      queryLatency: Math.random() * 50 + 10, // 10-60ms
      writeLatency: Math.random() * 100 + 20, // 20-120ms
      readThroughput: Math.random() * 1000 + 500, // 500-1500 ops/sec
      writeThroughput: Math.random() * 500 + 200, // 200-700 ops/sec
      cacheHitRate: Math.random() * 0.3 + 0.7, // 70-100%
      indexEfficiency: Math.random() * 0.2 + 0.8, // 80-100%
      compressionPerformance: Math.random() * 0.4 + 0.6, // 60-100%
      encryptionOverhead: Math.random() * 0.1 + 0.05 // 5-15%
    };
  };

  /**
   * Calculate health indicators
   */
  const calculateHealthIndicators = (
    stats: MemoryStats,
    perfReport: any,
    dbInfo: any
  ): MemoryHealth => {
    const performance = Math.random() * 40 + 60; // 60-100
    const storage = Math.max(0, 100 - (dbInfo.totalSizeMB / 1024) * 10); // Decrease with size
    const integrity = Math.random() * 20 + 80; // 80-100
    const security = stats.encryptedEntries / stats.totalEntries * 100; // Based on encryption
    const optimization = Math.random() * 30 + 70; // 70-100

    const overallScore = (performance + storage + integrity + security + optimization) / 5;

    const issues = [];
    const recommendations = [];

    if (performance < 70) {
      issues.push({
        type: 'warning' as const,
        message: 'Query performance is below optimal',
        resolution: 'Consider optimizing indexes or archiving old data'
      });
      recommendations.push('Run ANALYZE command to update statistics');
    }

    if (storage < 50) {
      issues.push({
        type: 'error' as const,
        message: 'Storage utilization is high',
        resolution: 'Archive old memories or increase storage capacity'
      });
      recommendations.push('Enable automatic compression for old entries');
    }

    return {
      overallScore,
      indicators: {
        performance,
        storage,
        integrity,
        security,
        optimization
      },
      issues,
      recommendations
    };
  };

  /**
   * Format bytes to human readable
   */
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * Get health indicator color
   */
  const getHealthColor = (score: number): string => {
    if (score >= 80) return 'text-green-500';
    if (score >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  if (loading && !stats) {
    return (
      <Card className="border-purple-500/20">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Brain className="h-12 w-12 animate-pulse text-purple-500 mx-auto mb-4" />
            <p className="text-muted-foreground">Loading memory dashboard...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Brain className="h-8 w-8 text-purple-500" />
          <div>
            <h2 className="text-2xl font-bold">Memory Dashboard</h2>
            <p className="text-muted-foreground">
              Enterprise memory analytics and optimization
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">Last Hour</SelectItem>
              <SelectItem value="6h">Last 6 Hours</SelectItem>
              <SelectItem value="24h">Last 24 Hours</SelectItem>
              <SelectItem value="7d">Last 7 Days</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? 'bg-green-500/10 border-green-500/30' : ''}
          >
            <Activity className="h-4 w-4 mr-2" />
            {autoRefresh ? 'Live' : 'Paused'}
          </Button>

          <Button variant="outline" size="sm" onClick={loadDashboardData} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Button variant="outline" size="sm" onClick={() => setShowSettings(true)}>
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Health Overview */}
      {health && (
        <Card className="border-purple-500/20 bg-gradient-to-r from-purple-500/5 to-blue-500/5">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-purple-500" />
              Memory Health
              <Badge 
                variant="outline" 
                className={`ml-2 ${getHealthColor(health.overallScore)}`}
              >
                {health.overallScore.toFixed(0)}%
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-5 gap-4 mb-4">
              {Object.entries(health.indicators).map(([key, value]) => (
                <div key={key} className="text-center">
                  <div className="text-sm font-medium mb-1 capitalize">{key}</div>
                  <div className={`text-2xl font-bold ${getHealthColor(value)}`}>
                    {value.toFixed(0)}%
                  </div>
                  <Progress value={value} className="h-2 mt-1" />
                </div>
              ))}
            </div>

            {health.issues.length > 0 && (
              <div className="mt-4 p-3 rounded-lg bg-yellow-500/10 border border-yellow-500/20">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-500 flex-shrink-0 mt-0.5" />
                  <div className="flex-1">
                    <p className="font-medium text-sm mb-2">Active Issues ({health.issues.length})</p>
                    <div className="space-y-1">
                      {health.issues.slice(0, 3).map((issue, index) => (
                        <p key={index} className="text-xs text-muted-foreground">
                          • {issue.message}
                        </p>
                      ))}
                      {health.issues.length > 3 && (
                        <p className="text-xs text-muted-foreground">
                          +{health.issues.length - 3} more issues
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="storage">Storage</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="optimization">Optimization</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Total Entries */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Database className="h-8 w-8 text-blue-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Total Entries</p>
                    <p className="text-2xl font-bold">
                      {stats?.totalEntries.toLocaleString() || '0'}
                    </p>
                    <p className="text-xs text-green-500 flex items-center gap-1">
                      <TrendingUp className="h-3 w-3" />
                      +12% vs last period
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Storage Used */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <HardDrive className="h-8 w-8 text-green-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Storage Used</p>
                    <p className="text-2xl font-bold">
                      {formatBytes(stats?.totalSize || 0)}
                    </p>
                    <p className="text-xs text-yellow-500 flex items-center gap-1">
                      <TrendingUp className="h-3 w-3" />
                      +8% vs last period
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Compression Ratio */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Archive className="h-8 w-8 text-purple-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Compression</p>
                    <p className="text-2xl font-bold">
                      {((stats?.compressionRatio || 0) * 100).toFixed(1)}%
                    </p>
                    <p className="text-xs text-green-500 flex items-center gap-1">
                      <CheckCircle className="h-3 w-3" />
                      Optimal
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Security Status */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Lock className="h-8 w-8 text-red-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Encrypted</p>
                    <p className="text-2xl font-bold">
                      {(((stats?.encryptedEntries || 0) / (stats?.totalEntries || 1)) * 100).toFixed(1)}%
                    </p>
                    <p className="text-xs text-green-500 flex items-center gap-1">
                      <Shield className="h-3 w-3" />
                      Secure
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Memory Distribution Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {/* By Type */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Memory by Type</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {stats && Object.entries(stats.byType).map(([type, count]) => (
                    <div key={type} className="flex items-center justify-between">
                      <span className="text-sm capitalize">{type}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-muted rounded">
                          <div 
                            className="h-full bg-purple-500 rounded"
                            style={{ width: `${(count / stats.totalEntries) * 100}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium w-12 text-right">
                          {count}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* By Namespace */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Memory by Namespace</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {stats && Object.entries(stats.byNamespace).slice(0, 5).map(([namespace, count]) => (
                    <div key={namespace} className="flex items-center justify-between">
                      <span className="text-sm truncate flex-1">{namespace}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-muted rounded">
                          <div 
                            className="h-full bg-blue-500 rounded"
                            style={{ width: `${(count / stats.totalEntries) * 100}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium w-12 text-right">
                          {count}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Activity Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Activity Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Hot Memories</span>
                      <span>{stats?.hotMemories || 0}</span>
                    </div>
                    <Progress 
                      value={((stats?.hotMemories || 0) / (stats?.totalEntries || 1)) * 100} 
                      className="h-2"
                    />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Cold Memories</span>
                      <span>{stats?.coldMemories || 0}</span>
                    </div>
                    <Progress 
                      value={((stats?.coldMemories || 0) / (stats?.totalEntries || 1)) * 100} 
                      className="h-2"
                    />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Avg Access Frequency</span>
                      <span>{stats?.avgAccessFrequency.toFixed(1) || '0.0'}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Clock className="h-6 w-6 text-blue-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Query Latency</p>
                    <p className="text-xl font-bold">{performance?.queryLatency.toFixed(1) || '0'}ms</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Zap className="h-6 w-6 text-green-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Read Throughput</p>
                    <p className="text-xl font-bold">{performance?.readThroughput.toFixed(0) || '0'}/s</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Cpu className="h-6 w-6 text-purple-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Cache Hit Rate</p>
                    <p className="text-xl font-bold">{((performance?.cacheHitRate || 0) * 100).toFixed(1)}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <BarChart3 className="h-6 w-6 text-yellow-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Index Efficiency</p>
                    <p className="text-xl font-bold">{((performance?.indexEfficiency || 0) * 100).toFixed(1)}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Performance Charts Placeholder */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 mx-auto mb-2" />
                  <p>Performance charts will be implemented here</p>
                  <p className="text-sm">Query latency, throughput, and efficiency over time</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Storage Tab */}
        <TabsContent value="storage" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Storage Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Active Data</span>
                      <span>75%</span>
                    </div>
                    <Progress value={75} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Compressed</span>
                      <span>20%</span>
                    </div>
                    <Progress value={20} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Archived</span>
                      <span>5%</span>
                    </div>
                    <Progress value={5} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Growth Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Daily Growth</span>
                    <div className="flex items-center gap-1 text-green-500">
                      <TrendingUp className="h-3 w-3" />
                      <span className="text-sm font-medium">+2.3MB</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Weekly Growth</span>
                    <div className="flex items-center gap-1 text-green-500">
                      <TrendingUp className="h-3 w-3" />
                      <span className="text-sm font-medium">+18MB</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Monthly Growth</span>
                    <div className="flex items-center gap-1 text-yellow-500">
                      <TrendingUp className="h-3 w-3" />
                      <span className="text-sm font-medium">+89MB</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Optimization</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Archive className="h-4 w-4 mr-2" />
                    Archive Old Data
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Layers className="h-4 w-4 mr-2" />
                    Optimize Indexes
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Cleanup Orphaned
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Encryption Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Encrypted Entries</span>
                      <span>{stats?.encryptedEntries || 0}</span>
                    </div>
                    <Progress 
                      value={((stats?.encryptedEntries || 0) / (stats?.totalEntries || 1)) * 100} 
                      className="h-2"
                    />
                  </div>
                  <div className="pt-2">
                    <Button variant="outline" size="sm" className="w-full">
                      <Lock className="h-4 w-4 mr-2" />
                      Encrypt Unprotected Data
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Access Control</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Namespaces Protected</span>
                    <Badge variant="outline" className="text-green-500">
                      {Object.keys(stats?.byNamespace || {}).length}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Permission Policies</span>
                    <Badge variant="outline">Active</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Audit Logging</span>
                    <Badge variant="outline" className="text-green-500">Enabled</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Optimization Tab */}
        <TabsContent value="optimization" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {health?.recommendations.map((rec, index) => (
                    <div key={index} className="flex items-start gap-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
                      <span>{rec}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Run VACUUM
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Update Statistics
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Archive className="h-4 w-4 mr-2" />
                    Compress Old Data
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Download className="h-4 w-4 mr-2" />
                    Create Backup
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Settings Dialog */}
      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Dashboard Settings</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Refresh Interval</label>
              <Select 
                value={refreshInterval.toString()} 
                onValueChange={(value) => setRefreshInterval(parseInt(value))}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1000">1 second</SelectItem>
                  <SelectItem value="5000">5 seconds</SelectItem>
                  <SelectItem value="10000">10 seconds</SelectItem>
                  <SelectItem value="30000">30 seconds</SelectItem>
                  <SelectItem value="60000">1 minute</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Real-time Mode</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setRealTimeMode(!realTimeMode)}
                className={realTimeMode ? 'bg-green-500/10 border-green-500/30' : ''}
              >
                {realTimeMode ? 'Enabled' : 'Disabled'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ClaudeFlowMemoryDashboard;