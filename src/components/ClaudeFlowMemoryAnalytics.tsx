/**
 * Claude-Flow v2.0.0 Memory Analytics Component
 * Advanced memory analytics, insights, and predictive analysis
 */

import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bar<PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  TrendingDown,
  Brain,
  Zap,
  Target,
  Clock,
  Users,
  Database,
  Activity,
  AlertTriangle,
  CheckCircle,
  ArrowUp,
  ArrowDown,
  Minus,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Settings,
  Eye,
  Lightbulb,
  Search,
  LineChart,
  Gauge,
  Layers,
  Network,
  CircuitBoard
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ClaudeFlowMemoryManager } from '@/lib/claudeFlowMemoryManager';
import { ClaudeFlowIntegrationV2 } from '@/lib/claudeFlowIntegrationV2';

interface MemoryAnalyticsProps {
  memoryManager: ClaudeFlowMemoryManager;
  integration: ClaudeFlowIntegrationV2;
}

interface AnalyticsData {
  overview: {
    totalMemories: number;
    storageUsed: number;
    avgImportance: number;
    accessFrequency: number;
    growthRate: number;
    compressionRatio: number;
  };
  trends: {
    memoryGrowth: TimeSeriesData[];
    accessPatterns: TimeSeriesData[];
    typeDistribution: TimeSeriesData[];
    agentActivity: TimeSeriesData[];
  };
  insights: {
    hotspots: MemoryHotspot[];
    patterns: AnalyticalPattern[];
    anomalies: MemoryAnomaly[];
    predictions: MemoryPrediction[];
  };
  performance: {
    queryLatency: number;
    writeLatency: number;
    indexEfficiency: number;
    cacheHitRate: number;
    compressionEfficiency: number;
    encryptionOverhead: number;
  };
  quality: {
    dataIntegrity: number;
    consistency: number;
    completeness: number;
    freshness: number;
    relevance: number;
  };
}

interface TimeSeriesData {
  timestamp: string;
  value: number;
  label: string;
  metadata?: any;
}

interface MemoryHotspot {
  id: string;
  type: 'agent' | 'namespace' | 'content_type' | 'time_period';
  identifier: string;
  activity: number;
  growth: number;
  importance: number;
  description: string;
}

interface AnalyticalPattern {
  id: string;
  type: 'usage' | 'content' | 'temporal' | 'behavioral';
  name: string;
  confidence: number;
  frequency: number;
  description: string;
  implications: string[];
  recommendations: string[];
}

interface MemoryAnomaly {
  id: string;
  type: 'spike' | 'drop' | 'outlier' | 'drift';
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  affected: string;
  description: string;
  impact: string;
  resolution?: string;
}

interface MemoryPrediction {
  id: string;
  type: 'growth' | 'usage' | 'performance' | 'storage';
  timeframe: '1d' | '1w' | '1m' | '3m' | '1y';
  prediction: number;
  confidence: number;
  factors: string[];
  implications: string[];
}

export const ClaudeFlowMemoryAnalytics: React.FC<MemoryAnalyticsProps> = ({
  memoryManager,
  integration
}) => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('overview');
  const [showSettings, setShowSettings] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30000);

  // Load analytics data
  useEffect(() => {
    loadAnalyticsData();
  }, [selectedTimeRange]);

  // Auto-refresh
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(loadAnalyticsData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  /**
   * Load comprehensive analytics data
   */
  const loadAnalyticsData = async () => {
    try {
      setLoading(true);

      // Get basic memory stats
      const memoryStats = await memoryManager.getMemoryStats();
      const dbInfo = memoryManager.getDatabaseInfo();
      
      // Get performance metrics
      const performanceReport = await integration.getPerformanceReport(selectedTimeRange as any);

      // Generate comprehensive analytics
      const analytics: AnalyticsData = {
        overview: {
          totalMemories: memoryStats.totalEntries,
          storageUsed: memoryStats.totalSize,
          avgImportance: 0.7, // Mock - would calculate from actual importance scores
          accessFrequency: 12.5, // Mock - would calculate from access patterns
          growthRate: 8.3, // Mock - percentage growth
          compressionRatio: 0.45 // Mock - compression effectiveness
        },
        trends: {
          memoryGrowth: generateMemoryGrowthTrend(),
          accessPatterns: generateAccessPatternTrend(),
          typeDistribution: generateTypeDistributionTrend(),
          agentActivity: generateAgentActivityTrend()
        },
        insights: {
          hotspots: await detectMemoryHotspots(memoryStats),
          patterns: await detectAnalyticalPatterns(memoryStats),
          anomalies: await detectMemoryAnomalies(memoryStats),
          predictions: generateMemoryPredictions(memoryStats)
        },
        performance: {
          queryLatency: performanceReport?.metrics?.avgResponseTime || 45,
          writeLatency: performanceReport?.metrics?.avgResponseTime * 1.5 || 67,
          indexEfficiency: 0.87,
          cacheHitRate: 0.73,
          compressionEfficiency: 0.82,
          encryptionOverhead: 0.12
        },
        quality: {
          dataIntegrity: 0.96,
          consistency: 0.91,
          completeness: 0.88,
          freshness: 0.85,
          relevance: 0.79
        }
      };

      setAnalyticsData(analytics);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Generate memory growth trend data
   */
  const generateMemoryGrowthTrend = (): TimeSeriesData[] => {
    const data: TimeSeriesData[] = [];
    const now = new Date();
    
    for (let i = 30; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const baseValue = 1000 + i * 50;
      const variation = Math.sin(i * 0.1) * 100 + Math.random() * 200;
      
      data.push({
        timestamp: date.toISOString(),
        value: Math.max(0, baseValue + variation),
        label: date.toLocaleDateString()
      });
    }
    
    return data;
  };

  /**
   * Generate access pattern trend data
   */
  const generateAccessPatternTrend = (): TimeSeriesData[] => {
    const data: TimeSeriesData[] = [];
    const now = new Date();
    
    for (let i = 24; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 60 * 60 * 1000);
      const baseValue = 50;
      const timeOfDay = date.getHours();
      
      // Higher activity during work hours
      const workHourMultiplier = timeOfDay >= 9 && timeOfDay <= 17 ? 1.5 : 0.7;
      const variation = Math.random() * 30;
      
      data.push({
        timestamp: date.toISOString(),
        value: Math.max(0, baseValue * workHourMultiplier + variation),
        label: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      });
    }
    
    return data;
  };

  /**
   * Generate type distribution trend
   */
  const generateTypeDistributionTrend = (): TimeSeriesData[] => {
    const types = ['observation', 'insight', 'decision', 'artifact', 'error'];
    return types.map(type => ({
      timestamp: new Date().toISOString(),
      value: Math.random() * 100,
      label: type,
      metadata: { color: getTypeColor(type) }
    }));
  };

  /**
   * Generate agent activity trend
   */
  const generateAgentActivityTrend = (): TimeSeriesData[] => {
    const agents = ['agent-001', 'agent-002', 'agent-003', 'agent-004', 'agent-005'];
    return agents.map(agent => ({
      timestamp: new Date().toISOString(),
      value: Math.random() * 200 + 50,
      label: agent
    }));
  };

  /**
   * Detect memory hotspots
   */
  const detectMemoryHotspots = async (stats: any): Promise<MemoryHotspot[]> => {
    const hotspots: MemoryHotspot[] = [];

    // Agent hotspots
    Object.entries(stats.byAgent || {}).forEach(([agent, count]) => {
      if (count > 50) { // Threshold for high activity
        hotspots.push({
          id: `agent-${agent}`,
          type: 'agent',
          identifier: agent,
          activity: count as number,
          growth: Math.random() * 20 + 5,
          importance: Math.random() * 0.5 + 0.5,
          description: `High memory activity detected for ${agent}`
        });
      }
    });

    // Namespace hotspots
    Object.entries(stats.byNamespace || {}).forEach(([namespace, count]) => {
      if (count > 30) {
        hotspots.push({
          id: `namespace-${namespace}`,
          type: 'namespace',
          identifier: namespace,
          activity: count as number,
          growth: Math.random() * 15 + 3,
          importance: Math.random() * 0.4 + 0.6,
          description: `Significant memory concentration in ${namespace} namespace`
        });
      }
    });

    return hotspots;
  };

  /**
   * Detect analytical patterns
   */
  const detectAnalyticalPatterns = async (stats: any): Promise<AnalyticalPattern[]> => {
    const patterns: AnalyticalPattern[] = [];

    // Usage pattern: Peak activity hours
    patterns.push({
      id: 'peak-hours',
      type: 'usage',
      name: 'Peak Activity Hours',
      confidence: 0.89,
      frequency: 5, // days per week
      description: 'Memory creation peaks between 10 AM - 2 PM',
      implications: ['Resource planning needed for peak hours', 'Higher compute demands during day shift'],
      recommendations: ['Scale resources during peak hours', 'Implement caching for common queries']
    });

    // Content pattern: Error clustering
    patterns.push({
      id: 'error-clustering',
      type: 'content',
      name: 'Error Memory Clustering',
      confidence: 0.76,
      frequency: 12, // occurrences per day
      description: 'Error memories tend to cluster around specific time periods',
      implications: ['System issues may be systemic', 'Error recovery patterns emerging'],
      recommendations: ['Implement proactive error prevention', 'Enhance error correlation analysis']
    });

    // Temporal pattern: Weekend memory drop
    patterns.push({
      id: 'weekend-drop',
      type: 'temporal',
      name: 'Weekend Activity Reduction',
      confidence: 0.95,
      frequency: 2, // times per week
      description: 'Memory creation drops 70% on weekends',
      implications: ['System usage reflects business hours', 'Maintenance windows available on weekends'],
      recommendations: ['Schedule maintenance during low activity', 'Implement weekend data archival']
    });

    return patterns;
  };

  /**
   * Detect memory anomalies
   */
  const detectMemoryAnomalies = async (stats: any): Promise<MemoryAnomaly[]> => {
    const anomalies: MemoryAnomaly[] = [];

    // Simulated anomaly detection
    anomalies.push({
      id: 'storage-spike-1',
      type: 'spike',
      severity: 'medium',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      affected: 'Storage Usage',
      description: 'Unusual spike in memory storage - 300% above baseline',
      impact: 'Potential storage capacity issues within 48 hours',
      resolution: 'Archive old memories or increase storage allocation'
    });

    anomalies.push({
      id: 'access-drop-1',
      type: 'drop',
      severity: 'low',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      affected: 'Access Frequency',
      description: 'Significant drop in memory access patterns',
      impact: 'Reduced system utilization, possible user issues',
      resolution: 'Monitor user engagement and system health'
    });

    return anomalies;
  };

  /**
   * Generate memory predictions
   */
  const generateMemoryPredictions = (stats: any): MemoryPrediction[] => {
    return [
      {
        id: 'growth-1w',
        type: 'growth',
        timeframe: '1w',
        prediction: stats.totalEntries * 1.15, // 15% growth
        confidence: 0.82,
        factors: ['Current growth trend', 'Agent activity levels', 'System adoption rate'],
        implications: ['Storage requirements will increase', 'Performance may be impacted']
      },
      {
        id: 'storage-1m',
        type: 'storage',
        timeframe: '1m',
        prediction: stats.totalSize * 1.6, // 60% storage growth
        confidence: 0.74,
        factors: ['Memory growth rate', 'Compression efficiency', 'Data archival policies'],
        implications: ['May need storage expansion', 'Consider implementing data lifecycle policies']
      },
      {
        id: 'performance-1w',
        type: 'performance',
        timeframe: '1w',
        prediction: 85, // Query latency in ms
        confidence: 0.69,
        factors: ['Current load trends', 'Index efficiency', 'Hardware capacity'],
        implications: ['Query performance may degrade', 'Index optimization needed']
      }
    ];
  };

  /**
   * Get type color
   */
  const getTypeColor = (type: string): string => {
    const colors = {
      observation: '#3B82F6',
      insight: '#F59E0B',
      decision: '#8B5CF6',
      artifact: '#10B981',
      error: '#EF4444'
    };
    return colors[type as keyof typeof colors] || '#6B7280';
  };

  /**
   * Get trend icon
   */
  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous * 1.05) return <ArrowUp className="h-4 w-4 text-green-500" />;
    if (current < previous * 0.95) return <ArrowDown className="h-4 w-4 text-red-500" />;
    return <Minus className="h-4 w-4 text-yellow-500" />;
  };

  /**
   * Format large numbers
   */
  const formatNumber = (num: number): string => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  if (loading && !analyticsData) {
    return (
      <Card className="border-purple-500/20">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 animate-pulse text-purple-500 mx-auto mb-4" />
            <p className="text-muted-foreground">Loading analytics...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <BarChart3 className="h-8 w-8 text-purple-500" />
          <div>
            <h2 className="text-2xl font-bold">Memory Analytics</h2>
            <p className="text-muted-foreground">
              Advanced insights and predictive analysis
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1d">Last Day</SelectItem>
              <SelectItem value="7d">Last Week</SelectItem>
              <SelectItem value="30d">Last Month</SelectItem>
              <SelectItem value="90d">Last Quarter</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? 'bg-green-500/10 border-green-500/30' : ''}
          >
            <Activity className="h-4 w-4 mr-2" />
            {autoRefresh ? 'Live' : 'Paused'}
          </Button>

          <Button variant="outline" size="sm" onClick={loadAnalyticsData} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Button variant="outline" size="sm" onClick={() => setShowSettings(true)}>
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      {analyticsData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Database className="h-6 w-6 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Memories</p>
                  <p className="text-xl font-bold">{formatNumber(analyticsData.overview.totalMemories)}</p>
                  <p className="text-xs text-green-500 flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    +{analyticsData.overview.growthRate.toFixed(1)}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Activity className="h-6 w-6 text-green-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Access Frequency</p>
                  <p className="text-xl font-bold">{analyticsData.overview.accessFrequency.toFixed(1)}</p>
                  <p className="text-xs text-green-500 flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    Active
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Target className="h-6 w-6 text-purple-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Avg Importance</p>
                  <p className="text-xl font-bold">{analyticsData.overview.avgImportance.toFixed(2)}</p>
                  <p className="text-xs text-yellow-500 flex items-center gap-1">
                    <Minus className="h-3 w-3" />
                    Stable
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Zap className="h-6 w-6 text-yellow-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Query Latency</p>
                  <p className="text-xl font-bold">{analyticsData.performance.queryLatency.toFixed(0)}ms</p>
                  <p className="text-xs text-green-500 flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    Good
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Layers className="h-6 w-6 text-orange-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Compression</p>
                  <p className="text-xl font-bold">{(analyticsData.overview.compressionRatio * 100).toFixed(0)}%</p>
                  <p className="text-xs text-green-500 flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    Optimal
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <CircuitBoard className="h-6 w-6 text-red-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Data Quality</p>
                  <p className="text-xl font-bold">{(analyticsData.quality.dataIntegrity * 100).toFixed(0)}%</p>
                  <p className="text-xs text-green-500 flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    Excellent
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Analytics */}
      <Tabs defaultValue="insights" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="quality">Quality</TabsTrigger>
          <TabsTrigger value="predictions">Predictions</TabsTrigger>
        </TabsList>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Memory Hotspots */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5 text-orange-500" />
                  Memory Hotspots
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-80">
                  <div className="space-y-3">
                    {analyticsData?.insights.hotspots.map(hotspot => (
                      <motion.div
                        key={hotspot.id}
                        className="p-3 rounded-lg border bg-card/50"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <Badge variant="outline" className="text-xs mb-1 capitalize">
                              {hotspot.type}
                            </Badge>
                            <p className="font-medium text-sm">{hotspot.identifier}</p>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold">{hotspot.activity}</div>
                            <div className="text-xs text-muted-foreground">activities</div>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {hotspot.description}
                        </p>
                        <div className="flex items-center gap-4 text-xs">
                          <div className="flex items-center gap-1">
                            <TrendingUp className="h-3 w-3 text-green-500" />
                            <span>+{hotspot.growth.toFixed(1)}% growth</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Target className="h-3 w-3 text-purple-500" />
                            <span>{(hotspot.importance * 100).toFixed(0)}% importance</span>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Analytical Patterns */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Network className="h-5 w-5 text-blue-500" />
                  Detected Patterns
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-80">
                  <div className="space-y-3">
                    {analyticsData?.insights.patterns.map(pattern => (
                      <motion.div
                        key={pattern.id}
                        className="p-3 rounded-lg border bg-card/50"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <Badge variant="outline" className="text-xs mb-1 capitalize">
                              {pattern.type}
                            </Badge>
                            <p className="font-medium text-sm">{pattern.name}</p>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold">{(pattern.confidence * 100).toFixed(0)}%</div>
                            <div className="text-xs text-muted-foreground">confidence</div>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {pattern.description}
                        </p>
                        {pattern.implications.length > 0 && (
                          <div className="mb-2">
                            <p className="text-xs font-medium mb-1">Implications:</p>
                            <ul className="text-xs space-y-1">
                              {pattern.implications.map((impl, index) => (
                                <li key={index} className="flex items-start gap-2">
                                  <Lightbulb className="h-3 w-3 text-yellow-500 flex-shrink-0 mt-0.5" />
                                  <span className="text-muted-foreground">{impl}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </motion.div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* Anomalies */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                Memory Anomalies
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {analyticsData?.insights.anomalies.map(anomaly => (
                  <motion.div
                    key={anomaly.id}
                    className={`p-3 rounded-lg border ${
                      anomaly.severity === 'critical' ? 'border-red-500/30 bg-red-500/5' :
                      anomaly.severity === 'high' ? 'border-orange-500/30 bg-orange-500/5' :
                      anomaly.severity === 'medium' ? 'border-yellow-500/30 bg-yellow-500/5' :
                      'border-blue-500/30 bg-blue-500/5'
                    }`}
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <Badge 
                          variant={anomaly.severity === 'critical' ? 'destructive' : 'outline'} 
                          className="text-xs mb-1 capitalize"
                        >
                          {anomaly.severity} {anomaly.type}
                        </Badge>
                        <p className="font-medium text-sm">{anomaly.affected}</p>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(anomaly.timestamp).toLocaleString()}
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {anomaly.description}
                    </p>
                    <p className="text-sm mb-2">
                      <strong>Impact:</strong> {anomaly.impact}
                    </p>
                    {anomaly.resolution && (
                      <p className="text-sm text-green-700">
                        <strong>Resolution:</strong> {anomaly.resolution}
                      </p>
                    )}
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Trends Tab */}
        <TabsContent value="trends" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Memory Growth Trend */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Memory Growth Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <LineChart className="h-12 w-12 mx-auto mb-2" />
                    <p className="text-sm">Growth trend chart</p>
                    <p className="text-xs">Showing {analyticsData?.trends.memoryGrowth.length} data points</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Access Patterns */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Access Patterns</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <Activity className="h-12 w-12 mx-auto mb-2" />
                    <p className="text-sm">Access pattern visualization</p>
                    <p className="text-xs">24-hour activity cycle</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Type Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Memory Type Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analyticsData?.trends.typeDistribution.map(item => (
                    <div key={item.label} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded" 
                          style={{ backgroundColor: item.metadata?.color }}
                        />
                        <span className="text-sm capitalize">{item.label}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-muted rounded">
                          <div 
                            className="h-full rounded"
                            style={{ 
                              backgroundColor: item.metadata?.color,
                              width: `${item.value}%` 
                            }}
                          />
                        </div>
                        <span className="text-sm font-medium w-12 text-right">
                          {item.value.toFixed(0)}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Agent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Agent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analyticsData?.trends.agentActivity.map(item => (
                    <div key={item.label} className="flex items-center justify-between">
                      <span className="text-sm font-mono">{item.label}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-24 h-2 bg-muted rounded">
                          <div 
                            className="h-full bg-blue-500 rounded"
                            style={{ width: `${(item.value / 250) * 100}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium w-12 text-right">
                          {item.value.toFixed(0)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3 mb-3">
                  <Clock className="h-6 w-6 text-blue-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Query Latency</p>
                    <p className="text-xl font-bold">{analyticsData?.performance.queryLatency.toFixed(1)}ms</p>
                  </div>
                </div>
                <Progress value={Math.max(0, 100 - analyticsData?.performance.queryLatency!)} className="h-2" />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3 mb-3">
                  <Zap className="h-6 w-6 text-green-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Cache Hit Rate</p>
                    <p className="text-xl font-bold">{(analyticsData?.performance.cacheHitRate! * 100).toFixed(1)}%</p>
                  </div>
                </div>
                <Progress value={analyticsData?.performance.cacheHitRate! * 100} className="h-2" />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3 mb-3">
                  <Gauge className="h-6 w-6 text-purple-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Index Efficiency</p>
                    <p className="text-xl font-bold">{(analyticsData?.performance.indexEfficiency! * 100).toFixed(1)}%</p>
                  </div>
                </div>
                <Progress value={analyticsData?.performance.indexEfficiency! * 100} className="h-2" />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Quality Tab */}
        <TabsContent value="quality" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {analyticsData && Object.entries(analyticsData.quality).map(([key, value]) => (
              <Card key={key}>
                <CardContent className="p-4 text-center">
                  <div className="text-sm font-medium mb-2 capitalize">{key.replace(/([A-Z])/g, ' $1')}</div>
                  <div className="text-2xl font-bold mb-2">{(value * 100).toFixed(0)}%</div>
                  <Progress value={value * 100} className="h-2" />
                  <div className={`text-xs mt-2 ${
                    value >= 0.9 ? 'text-green-500' : 
                    value >= 0.7 ? 'text-yellow-500' : 
                    'text-red-500'
                  }`}>
                    {value >= 0.9 ? 'Excellent' : value >= 0.7 ? 'Good' : 'Needs Improvement'}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Predictions Tab */}
        <TabsContent value="predictions" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {analyticsData?.insights.predictions.map(prediction => (
              <Card key={prediction.id}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-blue-500" />
                    {prediction.type.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} Prediction
                    <Badge variant="outline">{prediction.timeframe}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Predicted Value</span>
                      <span className="text-lg font-bold">
                        {prediction.type === 'growth' ? formatNumber(prediction.prediction) :
                         prediction.type === 'storage' ? formatBytes(prediction.prediction) :
                         prediction.prediction.toFixed(1)}
                        {prediction.type === 'performance' ? 'ms' : ''}
                      </span>
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm text-muted-foreground">Confidence</span>
                        <span className="text-sm font-medium">{(prediction.confidence * 100).toFixed(0)}%</span>
                      </div>
                      <Progress value={prediction.confidence * 100} className="h-2" />
                    </div>

                    <div>
                      <p className="text-sm font-medium mb-2">Key Factors:</p>
                      <ul className="text-sm space-y-1">
                        {prediction.factors.map((factor, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <Target className="h-3 w-3 text-muted-foreground flex-shrink-0 mt-0.5" />
                            <span>{factor}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <p className="text-sm font-medium mb-2">Implications:</p>
                      <ul className="text-sm space-y-1">
                        {prediction.implications.map((implication, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <Lightbulb className="h-3 w-3 text-yellow-500 flex-shrink-0 mt-0.5" />
                            <span>{implication}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Settings Dialog */}
      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Analytics Settings</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Refresh Interval</label>
              <Select 
                value={refreshInterval.toString()} 
                onValueChange={(value) => setRefreshInterval(parseInt(value))}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="15000">15 seconds</SelectItem>
                  <SelectItem value="30000">30 seconds</SelectItem>
                  <SelectItem value="60000">1 minute</SelectItem>
                  <SelectItem value="300000">5 minutes</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );

  function formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
};

export default ClaudeFlowMemoryAnalytics;