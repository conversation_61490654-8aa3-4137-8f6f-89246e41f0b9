/**
 * Claude-Flow v2.0.0 Queen Dashboard Component
 * Specialized view for Queen agent coordination and hive-mind oversight
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { <PERSON><PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { ScrollArea } from './ui/scroll-area';
import { Progress } from './ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from './ui/tabs';
import { Alert, AlertDescription } from './ui/alert';
import { 
  Crown, 
  Brain, 
  Users, 
  Activity,
  Target,
  Shield,
  Zap,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Network,
  Eye,
  MessageSquare,
  BarChart,
  Cpu,
  HardDrive,
  Wifi,
  GitBranch,
  Layers
} from 'lucide-react';
import { ClaudeFlowIntegrationV2 } from '../lib/claudeFlowIntegrationV2';
import type { 
  <PERSON><PERSON>lowAgent,
  Claude<PERSON>lowSwarmTopology,
  ClaudeFlowCognitivePulse
} from '../types/claudeFlowV2';

interface ClaudeFlowQueenDashboardProps {
  integration: ClaudeFlowIntegrationV2;
  sessionId: string;
}

interface SwarmTopology {
  clusters: Array<{
    id: string;
    name: string;
    agents: ClaudeFlowAgent[];
    load: number;
    efficiency: number;
  }>;
  connections: Array<{
    from: string;
    to: string;
    strength: number;
    messageCount: number;
  }>;
}

interface CognitivePulse {
  timestamp: string;
  insights: number;
  patterns: number;
  decisions: number;
  learningRate: number;
  confidenceScore: number;
}

interface QueenDirective {
  id: string;
  type: 'optimization' | 'rebalance' | 'spawn' | 'terminate' | 'priority';
  description: string;
  impact: 'low' | 'medium' | 'high';
  status: 'pending' | 'executing' | 'completed';
  timestamp: string;
}

export function ClaudeFlowQueenDashboard({ integration, sessionId }: ClaudeFlowQueenDashboardProps) {
  const [queenAgent, setQueenAgent] = useState<ClaudeFlowAgent | null>(null);
  const [topology, setTopology] = useState<SwarmTopology>({
    clusters: [],
    connections: []
  });
  const [cognitivePulse, setCognitivePulse] = useState<CognitivePulse[]>([]);
  const [directives, setDirectives] = useState<QueenDirective[]>([]);
  const [swarmHealth, setSwarmHealth] = useState({
    overall: 0,
    communication: 0,
    taskDistribution: 0,
    resourceUtilization: 0,
    neuralCoherence: 0
  });
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch Queen status and swarm data
  const fetchQueenData = useCallback(async () => {
    try {
      const status = await integration.getSwarmStatus(true);
      
      // Find Queen agent
      const queen = status.agents?.find((a: ClaudeFlowAgent) => a.type === 'queen');
      setQueenAgent(queen || null);
      
      // Update topology
      if (status.topology) {
        setTopology({
          clusters: status.topology.clusters || [],
          connections: status.topology.connections || []
        });
      }
      
      // Update cognitive pulse
      if (status.cognitivePulse) {
        setCognitivePulse(prev => [...prev.slice(-19), status.cognitivePulse].filter(Boolean));
      }
      
      // Update swarm health
      if (status.health) {
        setSwarmHealth({
          overall: status.health.overall || 0,
          communication: status.health.communication || 0,
          taskDistribution: status.health.taskDistribution || 0,
          resourceUtilization: status.health.resourceUtilization || 0,
          neuralCoherence: status.health.neuralCoherence || 0
        });
      }
    } catch (error) {
      console.error('Failed to fetch queen data:', error);
    }
  }, [integration]);

  // Auto-refresh
  useEffect(() => {
    fetchQueenData();
    const interval = setInterval(fetchQueenData, 3000); // More frequent updates for Queen
    return () => clearInterval(interval);
  }, [fetchQueenData]);

  // Issue directive
  const issueDirective = async (type: QueenDirective['type'], params?: any) => {
    setLoading(true);
    try {
      let description = '';
      switch (type) {
        case 'optimization':
          description = 'Optimize swarm performance and resource allocation';
          await integration.optimizeSwarmTopology();
          break;
        case 'rebalance':
          description = 'Rebalance agent workload distribution';
          await integration.rebalanceSwarmLoad();
          break;
        case 'spawn':
          description = `Spawn new ${params?.agentType || 'specialist'} agent`;
          await integration.createDynamicAgent({
            type: params?.agentType || 'specialist',
            capabilities: params?.capabilities || ['analysis', 'reporting'],
            resources: { priority: 7 }
          });
          break;
        case 'priority':
          description = 'Adjust task priorities based on current objectives';
          await integration.adjustSwarmPriorities(params?.adjustments);
          break;
      }
      
      const directive: QueenDirective = {
        id: `dir_${Date.now()}`,
        type,
        description,
        impact: type === 'spawn' || type === 'terminate' ? 'high' : 'medium',
        status: 'completed',
        timestamp: new Date().toISOString()
      };
      
      setDirectives(prev => [directive, ...prev.slice(0, 9)]);
      await fetchQueenData();
    } catch (error) {
      console.error('Failed to issue directive:', error);
    } finally {
      setLoading(false);
    }
  };

  // Calculate swarm efficiency
  const swarmEfficiency = topology.clusters.reduce((acc, cluster) => 
    acc + cluster.efficiency, 0) / Math.max(topology.clusters.length, 1);

  // Get health color
  const getHealthColor = (value: number) => {
    if (value >= 80) return 'text-green-500';
    if (value >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  // Get latest cognitive metrics
  const latestCognitive = cognitivePulse[cognitivePulse.length - 1] || {
    insights: 0,
    patterns: 0,
    decisions: 0,
    learningRate: 0,
    confidenceScore: 0
  };

  return (
    <div className="space-y-4">
      {/* Queen Header */}
      <Card className="border-yellow-500/20 bg-gradient-to-r from-yellow-500/5 to-transparent">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Crown className="h-8 w-8 text-yellow-500" />
              <div>
                <CardTitle className="text-xl">Queen Agent Command Center</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Hive-Mind Orchestration & Swarm Intelligence
                </p>
              </div>
            </div>
            {queenAgent && (
              <Badge variant="outline" className="border-yellow-500/50">
                {queenAgent.status}
              </Badge>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Overall Health</span>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </div>
              <p className={`text-2xl font-bold ${getHealthColor(swarmHealth.overall)}`}>
                {swarmHealth.overall.toFixed(0)}%
              </p>
              <Progress value={swarmHealth.overall} className="h-1" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Communication</span>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </div>
              <p className={`text-2xl font-bold ${getHealthColor(swarmHealth.communication)}`}>
                {swarmHealth.communication.toFixed(0)}%
              </p>
              <Progress value={swarmHealth.communication} className="h-1" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Task Distribution</span>
                <Target className="h-4 w-4 text-muted-foreground" />
              </div>
              <p className={`text-2xl font-bold ${getHealthColor(swarmHealth.taskDistribution)}`}>
                {swarmHealth.taskDistribution.toFixed(0)}%
              </p>
              <Progress value={swarmHealth.taskDistribution} className="h-1" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Resources</span>
                <Cpu className="h-4 w-4 text-muted-foreground" />
              </div>
              <p className={`text-2xl font-bold ${getHealthColor(swarmHealth.resourceUtilization)}`}>
                {swarmHealth.resourceUtilization.toFixed(0)}%
              </p>
              <Progress value={swarmHealth.resourceUtilization} className="h-1" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Neural Coherence</span>
                <Brain className="h-4 w-4 text-muted-foreground" />
              </div>
              <p className={`text-2xl font-bold ${getHealthColor(swarmHealth.neuralCoherence)}`}>
                {swarmHealth.neuralCoherence.toFixed(0)}%
              </p>
              <Progress value={swarmHealth.neuralCoherence} className="h-1" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardContent className="p-0">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="w-full justify-start rounded-none border-b">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="topology">Swarm Topology</TabsTrigger>
              <TabsTrigger value="cognitive">Cognitive Pulse</TabsTrigger>
              <TabsTrigger value="directives">Queen Directives</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="p-4">
              <div className="space-y-4">
                {/* Quick Actions */}
                <div className="flex flex-wrap gap-2">
                  <Button
                    size="sm"
                    onClick={() => issueDirective('optimization')}
                    disabled={loading}
                  >
                    <Zap className="h-4 w-4 mr-1" />
                    Optimize Swarm
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => issueDirective('rebalance')}
                    disabled={loading}
                  >
                    <GitBranch className="h-4 w-4 mr-1" />
                    Rebalance Load
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => issueDirective('spawn', { 
                      agentType: 'analyst',
                      capabilities: ['deep-analysis', 'pattern-recognition']
                    })}
                    disabled={loading}
                  >
                    <Users className="h-4 w-4 mr-1" />
                    Spawn Analyst
                  </Button>
                </div>

                {/* Cognitive Metrics */}
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  <div className="space-y-1">
                    <span className="text-sm text-muted-foreground">Insights/min</span>
                    <p className="text-xl font-bold">{latestCognitive.insights}</p>
                  </div>
                  <div className="space-y-1">
                    <span className="text-sm text-muted-foreground">Patterns</span>
                    <p className="text-xl font-bold">{latestCognitive.patterns}</p>
                  </div>
                  <div className="space-y-1">
                    <span className="text-sm text-muted-foreground">Decisions</span>
                    <p className="text-xl font-bold">{latestCognitive.decisions}</p>
                  </div>
                  <div className="space-y-1">
                    <span className="text-sm text-muted-foreground">Learning Rate</span>
                    <p className="text-xl font-bold">{(latestCognitive.learningRate * 100).toFixed(1)}%</p>
                  </div>
                  <div className="space-y-1">
                    <span className="text-sm text-muted-foreground">Confidence</span>
                    <p className="text-xl font-bold">{(latestCognitive.confidenceScore * 100).toFixed(1)}%</p>
                  </div>
                </div>

                {/* Swarm Summary */}
                <div className="rounded-lg border p-4 space-y-3">
                  <h3 className="font-semibold flex items-center gap-2">
                    <Network className="h-4 w-4" />
                    Swarm Summary
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Active Clusters:</span>
                      <p className="font-medium">{topology.clusters.length}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Total Agents:</span>
                      <p className="font-medium">
                        {topology.clusters.reduce((acc, c) => acc + c.agents.length, 0)}
                      </p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Avg Efficiency:</span>
                      <p className="font-medium">{swarmEfficiency.toFixed(1)}%</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Active Connections:</span>
                      <p className="font-medium">{topology.connections.length}</p>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Topology Tab */}
            <TabsContent value="topology" className="p-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold">Swarm Topology</h3>
                  <Badge variant="outline">
                    {topology.clusters.length} clusters
                  </Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {topology.clusters.map((cluster) => (
                    <Card key={cluster.id}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">{cluster.name}</h4>
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary">{cluster.agents.length} agents</Badge>
                            <Badge variant={cluster.efficiency > 80 ? 'success' : 'warning'}>
                              {cluster.efficiency.toFixed(0)}% eff
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">Load:</span>
                            <div className="flex items-center gap-2">
                              <Progress value={cluster.load} className="w-20 h-2" />
                              <span>{cluster.load.toFixed(0)}%</span>
                            </div>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {cluster.agents.slice(0, 5).map((agent) => (
                              <Badge key={agent.id} variant="outline" className="text-xs">
                                {agent.type}
                              </Badge>
                            ))}
                            {cluster.agents.length > 5 && (
                              <Badge variant="outline" className="text-xs">
                                +{cluster.agents.length - 5}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Connection Strength */}
                <Card>
                  <CardHeader className="pb-3">
                    <h4 className="font-medium">Inter-Cluster Communication</h4>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {topology.connections.slice(0, 5).map((conn, i) => (
                        <div key={i} className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">
                            {conn.from} ↔ {conn.to}
                          </span>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{conn.messageCount} msgs</Badge>
                            <Progress value={conn.strength * 100} className="w-20 h-2" />
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Cognitive Pulse Tab */}
            <TabsContent value="cognitive" className="p-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold">Cognitive Pulse</h3>
                  <Badge variant="outline" className="animate-pulse">
                    Live
                  </Badge>
                </div>

                {/* Pulse Chart (simplified representation) */}
                <Card>
                  <CardContent className="p-4">
                    <div className="h-40 flex items-end justify-between gap-1">
                      {cognitivePulse.map((pulse, i) => (
                        <div
                          key={i}
                          className="flex-1 bg-primary/20 rounded-t"
                          style={{
                            height: `${(pulse.confidenceScore * 100)}%`,
                            opacity: 0.3 + (i / cognitivePulse.length) * 0.7
                          }}
                        />
                      ))}
                    </div>
                    <p className="text-xs text-muted-foreground text-center mt-2">
                      Confidence Score Timeline
                    </p>
                  </CardContent>
                </Card>

                {/* Current Cognitive State */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-3">
                      <h4 className="font-medium">Pattern Recognition</h4>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Active Patterns:</span>
                          <span className="font-medium">{latestCognitive.patterns}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Learning Rate:</span>
                          <Badge variant={latestCognitive.learningRate > 0.7 ? 'success' : 'secondary'}>
                            {(latestCognitive.learningRate * 100).toFixed(1)}%
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <h4 className="font-medium">Decision Making</h4>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Decisions/min:</span>
                          <span className="font-medium">{latestCognitive.decisions}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Confidence:</span>
                          <Badge variant={latestCognitive.confidenceScore > 0.8 ? 'success' : 'secondary'}>
                            {(latestCognitive.confidenceScore * 100).toFixed(1)}%
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            {/* Directives Tab */}
            <TabsContent value="directives" className="p-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold">Recent Queen Directives</h3>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setDirectives([])}
                  >
                    Clear History
                  </Button>
                </div>

                <ScrollArea className="h-[400px]">
                  <div className="space-y-2">
                    {directives.length === 0 ? (
                      <p className="text-center text-muted-foreground py-8">
                        No directives issued yet
                      </p>
                    ) : (
                      directives.map((directive) => (
                        <Card key={directive.id}>
                          <CardContent className="p-3">
                            <div className="flex items-start justify-between">
                              <div className="space-y-1 flex-1">
                                <div className="flex items-center gap-2">
                                  <Badge variant={
                                    directive.impact === 'high' ? 'destructive' :
                                    directive.impact === 'medium' ? 'default' : 'secondary'
                                  }>
                                    {directive.type}
                                  </Badge>
                                  <Badge variant={
                                    directive.status === 'completed' ? 'success' :
                                    directive.status === 'executing' ? 'default' : 'secondary'
                                  }>
                                    {directive.status}
                                  </Badge>
                                </div>
                                <p className="text-sm">{directive.description}</p>
                                <p className="text-xs text-muted-foreground">
                                  {new Date(directive.timestamp).toLocaleTimeString()}
                                </p>
                              </div>
                              {directive.status === 'completed' && (
                                <CheckCircle className="h-4 w-4 text-green-500 mt-1" />
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      ))
                    )}
                  </div>
                </ScrollArea>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}