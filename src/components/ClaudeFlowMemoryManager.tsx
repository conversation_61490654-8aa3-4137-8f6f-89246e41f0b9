/**
 * Claude-Flow v2.0.0 Memory Management Hub
 * Comprehensive memory management interface integrating all memory components
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain,
  Database,
  BarChart3,
  Network,
  Settings,
  Download,
  Upload,
  Archive,
  Shield,
  RefreshCw,
  Search,
  Filter,
  Eye,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  HardDrive,
  Activity,
  Layers,
  Maximize2,
  Minimize2,
  Plus,
  MoreVertical,
  Trash2,
  Edit3,
  Lock,
  Unlock,
  Share2,
  Copy
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Textarea } from '@/components/ui/textarea';
import { ClaudeFlowMemoryManager } from '@/lib/claudeFlowMemoryManager';
import { ClaudeFlowIntegrationV2 } from '@/lib/claudeFlowIntegrationV2';
import ClaudeFlowMemoryDashboard from './ClaudeFlowMemoryDashboard';
import ClaudeFlowMemoryVisualization from './ClaudeFlowMemoryVisualization';
import ClaudeFlowMemoryAnalytics from './ClaudeFlowMemoryAnalytics';
import ClaudeFlowMemory from './ClaudeFlowMemory';

interface MemoryManagerProps {
  memoryManager: ClaudeFlowMemoryManager;
  integration: ClaudeFlowIntegrationV2;
  onClose?: () => void;
}

interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  description: string;
  action: () => void;
  danger?: boolean;
}

interface BackupOperation {
  id: string;
  type: 'backup' | 'restore' | 'export' | 'import';
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  filename?: string;
  size?: number;
  timestamp: string;
  error?: string;
}

export const ClaudeFlowMemoryManager: React.FC<MemoryManagerProps> = ({
  memoryManager,
  integration,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [systemHealth, setSystemHealth] = useState({
    status: 'healthy',
    score: 95,
    issues: 0,
    warnings: 2
  });
  const [operations, setOperations] = useState<BackupOperation[]>([]);
  const [showBackupDialog, setShowBackupDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [showSettingsDialog, setShowSettingsDialog] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);

  // Memory statistics
  const [memoryStats, setMemoryStats] = useState({
    totalEntries: 0,
    totalSize: 0,
    activeNamespaces: 0,
    compressionRatio: 0,
    encryptedPercentage: 0,
    avgQueryTime: 0
  });

  // Load initial data
  useEffect(() => {
    loadMemoryStats();
    loadSystemHealth();
  }, []);

  /**
   * Load memory statistics
   */
  const loadMemoryStats = async () => {
    try {
      setLoading(true);
      const stats = await memoryManager.getMemoryStats();
      const dbInfo = memoryManager.getDatabaseInfo();

      setMemoryStats({
        totalEntries: stats.totalEntries,
        totalSize: stats.totalSize,
        activeNamespaces: Object.keys(stats.byNamespace).length,
        compressionRatio: 0.35, // Mock - would calculate from actual data
        encryptedPercentage: 0.78, // Mock - would calculate from actual data
        avgQueryTime: 45 // Mock - would get from performance metrics
      });
    } catch (error) {
      console.error('Failed to load memory stats:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load system health metrics
   */
  const loadSystemHealth = async () => {
    try {
      // Mock health calculation - in real implementation would analyze:
      // - Database integrity
      // - Performance metrics
      // - Storage usage
      // - Error rates
      // - Security status
      
      const healthScore = Math.floor(Math.random() * 20 + 80); // 80-100
      const issues = Math.floor(Math.random() * 3); // 0-2
      const warnings = Math.floor(Math.random() * 5); // 0-4

      setSystemHealth({
        status: healthScore >= 90 ? 'healthy' : healthScore >= 70 ? 'warning' : 'critical',
        score: healthScore,
        issues,
        warnings
      });
    } catch (error) {
      console.error('Failed to load system health:', error);
    }
  };

  /**
   * Quick actions available in the manager
   */
  const quickActions: QuickAction[] = [
    {
      id: 'backup',
      label: 'Create Backup',
      icon: <Archive className="h-4 w-4" />,
      description: 'Create a full memory database backup',
      action: () => handleCreateBackup()
    },
    {
      id: 'optimize',
      label: 'Optimize Database',
      icon: <Zap className="h-4 w-4" />,
      description: 'Run database optimization and cleanup',
      action: () => handleOptimizeDatabase()
    },
    {
      id: 'compress',
      label: 'Compress Old Data',
      icon: <Layers className="h-4 w-4" />,
      description: 'Compress memories older than 30 days',
      action: () => handleCompressOldData()
    },
    {
      id: 'export',
      label: 'Export Data',
      icon: <Download className="h-4 w-4" />,
      description: 'Export memory data in various formats',
      action: () => handleExportData()
    },
    {
      id: 'import',
      label: 'Import Data',
      icon: <Upload className="h-4 w-4" />,
      description: 'Import memory data from external sources',
      action: () => setShowImportDialog(true)
    },
    {
      id: 'security-scan',
      label: 'Security Scan',
      icon: <Shield className="h-4 w-4" />,
      description: 'Run comprehensive security analysis',
      action: () => handleSecurityScan()
    },
    {
      id: 'cleanup',
      label: 'Cleanup Orphaned',
      icon: <Trash2 className="h-4 w-4" />,
      description: 'Remove orphaned and duplicate memories',
      action: () => handleCleanupOrphaned(),
      danger: true
    }
  ];

  /**
   * Handle backup creation
   */
  const handleCreateBackup = useCallback(async () => {
    const operation: BackupOperation = {
      id: `backup-${Date.now()}`,
      type: 'backup',
      status: 'running',
      progress: 0,
      timestamp: new Date().toISOString(),
      filename: `memory-backup-${new Date().toISOString().split('T')[0]}.db`
    };

    setOperations(prev => [...prev, operation]);

    try {
      // Simulate backup progress
      for (let progress = 0; progress <= 100; progress += 10) {
        setOperations(prev => 
          prev.map(op => 
            op.id === operation.id 
              ? { ...op, progress }
              : op
          )
        );
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      const backupPath = await memoryManager.createBackup();
      
      setOperations(prev => 
        prev.map(op => 
          op.id === operation.id 
            ? { 
                ...op, 
                status: 'completed', 
                progress: 100,
                filename: backupPath.split('/').pop()
              }
            : op
        )
      );
    } catch (error) {
      setOperations(prev => 
        prev.map(op => 
          op.id === operation.id 
            ? { ...op, status: 'failed', error: error.message }
            : op
        )
      );
    }
  }, [memoryManager]);

  /**
   * Handle database optimization
   */
  const handleOptimizeDatabase = useCallback(async () => {
    const operation: BackupOperation = {
      id: `optimize-${Date.now()}`,
      type: 'export', // Using export as generic operation type
      status: 'running',
      progress: 0,
      timestamp: new Date().toISOString()
    };

    setOperations(prev => [...prev, operation]);

    try {
      // Simulate optimization
      for (let progress = 0; progress <= 100; progress += 25) {
        setOperations(prev => 
          prev.map(op => 
            op.id === operation.id 
              ? { ...op, progress }
              : op
          )
        );
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      await memoryManager.optimize();
      
      setOperations(prev => 
        prev.map(op => 
          op.id === operation.id 
            ? { ...op, status: 'completed', progress: 100 }
            : op
        )
      );

      // Reload stats after optimization
      await loadMemoryStats();
    } catch (error) {
      setOperations(prev => 
        prev.map(op => 
          op.id === operation.id 
            ? { ...op, status: 'failed', error: error.message }
            : op
        )
      );
    }
  }, [memoryManager]);

  /**
   * Handle old data compression
   */
  const handleCompressOldData = useCallback(async () => {
    // Implementation would compress old memories
    console.log('Compressing old data...');
  }, []);

  /**
   * Handle data export
   */
  const handleExportData = useCallback(() => {
    setShowBackupDialog(true);
  }, []);

  /**
   * Handle security scan
   */
  const handleSecurityScan = useCallback(async () => {
    try {
      await integration.runSecurityScan('memory-system');
      await loadSystemHealth();
    } catch (error) {
      console.error('Security scan failed:', error);
    }
  }, [integration]);

  /**
   * Handle orphaned data cleanup
   */
  const handleCleanupOrphaned = useCallback(async () => {
    // Implementation would remove orphaned memories
    console.log('Cleaning up orphaned data...');
  }, []);

  /**
   * Format bytes to human readable
   */
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * Get health status color
   */
  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-500';
      case 'warning': return 'text-yellow-500';
      case 'critical': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const containerClasses = isFullscreen 
    ? 'fixed inset-0 z-50 bg-background' 
    : 'w-full';

  return (
    <div className={containerClasses}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="h-full flex flex-col space-y-6 p-6"
      >
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Brain className="h-8 w-8 text-purple-500" />
            <div>
              <h1 className="text-3xl font-bold">Memory Management</h1>
              <p className="text-muted-foreground">
                Enterprise-grade memory system management and analytics
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* System Health Indicator */}
            <div className="flex items-center gap-2 px-3 py-1 rounded-lg bg-card border">
              <div className={`w-2 h-2 rounded-full ${
                systemHealth.status === 'healthy' ? 'bg-green-500' :
                systemHealth.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
              }`} />
              <span className="text-sm font-medium">
                System {systemHealth.status} ({systemHealth.score}%)
              </span>
            </div>

            {/* Quick Actions Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Quick Actions
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                {quickActions.map(action => (
                  <DropdownMenuItem
                    key={action.id}
                    onClick={action.action}
                    className={action.danger ? 'text-destructive focus:text-destructive' : ''}
                  >
                    {action.icon}
                    <span className="ml-2">{action.label}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFullscreen(!isFullscreen)}
            >
              {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>

            <Button variant="outline" size="sm" onClick={() => setShowSettingsDialog(true)}>
              <Settings className="h-4 w-4" />
            </Button>

            {onClose && !isFullscreen && (
              <Button variant="outline" size="sm" onClick={onClose}>
                ×
              </Button>
            )}
          </div>
        </div>

        {/* Quick Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Database className="h-6 w-6 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Memories</p>
                  <p className="text-xl font-bold">{memoryStats.totalEntries.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <HardDrive className="h-6 w-6 text-green-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Storage Used</p>
                  <p className="text-xl font-bold">{formatBytes(memoryStats.totalSize)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Layers className="h-6 w-6 text-purple-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Namespaces</p>
                  <p className="text-xl font-bold">{memoryStats.activeNamespaces}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Archive className="h-6 w-6 text-orange-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Compression</p>
                  <p className="text-xl font-bold">{(memoryStats.compressionRatio * 100).toFixed(0)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Shield className="h-6 w-6 text-red-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Encrypted</p>
                  <p className="text-xl font-bold">{(memoryStats.encryptedPercentage * 100).toFixed(0)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Clock className="h-6 w-6 text-yellow-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Avg Query</p>
                  <p className="text-xl font-bold">{memoryStats.avgQueryTime}ms</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Active Operations */}
        <AnimatePresence>
          {operations.length > 0 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
            >
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Active Operations</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {operations.filter(op => op.status !== 'completed').map(operation => (
                      <div key={operation.id} className="flex items-center gap-3 p-2 rounded bg-card/50">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium capitalize">{operation.type}</span>
                            <Badge variant={
                              operation.status === 'running' ? 'default' :
                              operation.status === 'completed' ? 'outline' :
                              operation.status === 'failed' ? 'destructive' : 'secondary'
                            }>
                              {operation.status}
                            </Badge>
                          </div>
                          {operation.status === 'running' && (
                            <Progress value={operation.progress} className="h-2" />
                          )}
                          {operation.error && (
                            <p className="text-xs text-destructive mt-1">{operation.error}</p>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {operation.status === 'running' ? `${operation.progress}%` : operation.status}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Content Tabs */}
        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">
                <BarChart3 className="h-4 w-4 mr-2" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="memories">
                <Database className="h-4 w-4 mr-2" />
                Memories
              </TabsTrigger>
              <TabsTrigger value="visualization">
                <Network className="h-4 w-4 mr-2" />
                Visualization
              </TabsTrigger>
              <TabsTrigger value="analytics">
                <BarChart3 className="h-4 w-4 mr-2" />
                Analytics
              </TabsTrigger>
              <TabsTrigger value="dashboard">
                <Activity className="h-4 w-4 mr-2" />
                Dashboard
              </TabsTrigger>
            </TabsList>

            <div className="flex-1 mt-4 overflow-hidden">
              <TabsContent value="overview" className="h-full">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
                  {/* System Health */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Activity className="h-5 w-5 text-green-500" />
                        System Health
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="text-center">
                          <div className={`text-4xl font-bold ${getHealthColor(systemHealth.status)}`}>
                            {systemHealth.score}%
                          </div>
                          <div className="text-sm text-muted-foreground capitalize">
                            {systemHealth.status}
                          </div>
                        </div>
                        
                        <Progress value={systemHealth.score} className="h-3" />
                        
                        <div className="grid grid-cols-2 gap-4 text-center">
                          <div>
                            <div className="text-2xl font-bold text-red-500">{systemHealth.issues}</div>
                            <div className="text-xs text-muted-foreground">Issues</div>
                          </div>
                          <div>
                            <div className="text-2xl font-bold text-yellow-500">{systemHealth.warnings}</div>
                            <div className="text-xs text-muted-foreground">Warnings</div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Quick Actions */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="h-5 w-5 text-purple-500" />
                        Quick Actions
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-64">
                        <div className="grid grid-cols-1 gap-2">
                          {quickActions.map(action => (
                            <Button
                              key={action.id}
                              variant="outline"
                              size="sm"
                              onClick={action.action}
                              className={`justify-start ${action.danger ? 'text-destructive hover:text-destructive' : ''}`}
                            >
                              {action.icon}
                              <span className="ml-2">{action.label}</span>
                            </Button>
                          ))}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>

                  {/* Recent Operations */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Clock className="h-5 w-5 text-blue-500" />
                        Recent Operations
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-64">
                        <div className="space-y-2">
                          {operations.length > 0 ? (
                            operations.map(operation => (
                              <div key={operation.id} className="flex items-center justify-between p-2 rounded bg-card/30">
                                <div>
                                  <div className="text-sm font-medium capitalize">{operation.type}</div>
                                  <div className="text-xs text-muted-foreground">
                                    {new Date(operation.timestamp).toLocaleString()}
                                  </div>
                                </div>
                                <Badge variant={
                                  operation.status === 'completed' ? 'outline' :
                                  operation.status === 'failed' ? 'destructive' : 'default'
                                }>
                                  {operation.status}
                                </Badge>
                              </div>
                            ))
                          ) : (
                            <div className="text-center py-8 text-muted-foreground">
                              <Clock className="h-8 w-8 mx-auto mb-2" />
                              <p className="text-sm">No recent operations</p>
                            </div>
                          )}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="memories" className="h-full">
                <ClaudeFlowMemory integration={integration} />
              </TabsContent>

              <TabsContent value="visualization" className="h-full">
                <ClaudeFlowMemoryVisualization 
                  memoryManager={memoryManager} 
                  integration={integration} 
                />
              </TabsContent>

              <TabsContent value="analytics" className="h-full">
                <ClaudeFlowMemoryAnalytics 
                  memoryManager={memoryManager} 
                  integration={integration} 
                />
              </TabsContent>

              <TabsContent value="dashboard" className="h-full">
                <ClaudeFlowMemoryDashboard 
                  memoryManager={memoryManager} 
                  integration={integration} 
                />
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </motion.div>

      {/* Settings Dialog */}
      <Dialog open={showSettingsDialog} onOpenChange={setShowSettingsDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Memory Management Settings</DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">System Configuration</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Auto-compression</p>
                    <p className="text-sm text-muted-foreground">Automatically compress old memories</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Memory encryption</p>
                    <p className="text-sm text-muted-foreground">Encrypt sensitive memory entries</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Auto-backup</p>
                    <p className="text-sm text-muted-foreground">Create automatic backups daily</p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Backup Dialog */}
      <Dialog open={showBackupDialog} onOpenChange={setShowBackupDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Export Memory Data</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Export Format</label>
              <Select defaultValue="full">
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="full">Full Database Backup</SelectItem>
                  <SelectItem value="json">JSON Export</SelectItem>
                  <SelectItem value="csv">CSV Export</SelectItem>
                  <SelectItem value="xml">XML Export</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBackupDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => {
              setShowBackupDialog(false);
              handleCreateBackup();
            }}>
              Export
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Import Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Memory Data</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Import Source</label>
              <Select defaultValue="file">
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="file">Local File</SelectItem>
                  <SelectItem value="url">Remote URL</SelectItem>
                  <SelectItem value="backup">Backup File</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">File Path</label>
              <Input placeholder="Select file to import..." />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowImportDialog(false)}>
              Cancel
            </Button>
            <Button>Import</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ClaudeFlowMemoryManager;