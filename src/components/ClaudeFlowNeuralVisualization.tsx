/**
 * Claude-Flow v2.0.0 Neural Network Visualization Component
 * Interactive model architecture visualization with training progress and decision boundaries
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Network,
  Layers,
  Eye,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Download,
  Settings,
  Play,
  Pause,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Circle,
  Square,
  Triangle,
  GitBranch,
  Activity,
  Zap,
  Brain,
  Target,
  X,
  Maximize2,
  Minimize2,
  <PERSON><PERSON><PERSON>er,
  Move3D,
  Palette,
  Filter,
  Grid,
  Camera
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

interface NeuralVisualizationProps {
  models: any[];
  selectedModel: string | null;
  onModelSelect: (modelId: string) => void;
  onClose: () => void;
}

interface NodeData {
  id: string;
  layer: number;
  position: { x: number; y: number; z?: number };
  activation: number;
  weight?: number;
  bias?: number;
  type: 'input' | 'hidden' | 'output';
  connections: ConnectionData[];
}

interface ConnectionData {
  id: string;
  from: string;
  to: string;
  weight: number;
  strength: number;
  active: boolean;
}

interface LayerData {
  id: string;
  index: number;
  type: 'input' | 'hidden' | 'output' | 'conv' | 'pool' | 'attention';
  neurons: number;
  activation: string;
  parameters: number;
  outputShape: number[];
}

interface VisualizationConfig {
  viewMode: '2d' | '3d' | 'graph';
  showWeights: boolean;
  showActivations: boolean;
  showGradients: boolean;
  animateForward: boolean;
  colorScheme: 'default' | 'heatmap' | 'categorical' | 'gradient';
  nodeSize: number;
  connectionOpacity: number;
  layerSpacing: number;
  neuronSpacing: number;
  showLabels: boolean;
  highlightPath: boolean;
}

export const ClaudeFlowNeuralVisualization: React.FC<NeuralVisualizationProps> = ({
  models,
  selectedModel,
  onModelSelect,
  onClose
}) => {
  const [config, setConfig] = useState<VisualizationConfig>({
    viewMode: '2d',
    showWeights: true,
    showActivations: true,
    showGradients: false,
    animateForward: true,
    colorScheme: 'default',
    nodeSize: 1,
    connectionOpacity: 0.7,
    layerSpacing: 150,
    neuronSpacing: 50,
    showLabels: true,
    highlightPath: false
  });

  const [isPlaying, setIsPlaying] = useState(false);
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState({ x: 0, y: 0, z: 0 });
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);
  const [nodes, setNodes] = useState<NodeData[]>([]);
  const [connections, setConnections] = useState<ConnectionData[]>([]);
  const [layers, setLayers] = useState<LayerData[]>([]);
  const [trainingData, setTrainingData] = useState({
    epochs: [],
    loss: [],
    accuracy: [],
    weights: []
  });

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();

  // Generate mock neural network data
  const generateNetworkData = useCallback((model: any) => {
    if (!model) return;

    const layerData: LayerData[] = [];
    const nodeData: NodeData[] = [];
    const connectionData: ConnectionData[] = [];

    // Create layers
    const layerSizes = model.architecture?.neuronsPerLayer || [10, 20, 15, 5];
    layerSizes.forEach((size, layerIndex) => {
      layerData.push({
        id: `layer-${layerIndex}`,
        index: layerIndex,
        type: layerIndex === 0 ? 'input' : 
              layerIndex === layerSizes.length - 1 ? 'output' : 'hidden',
        neurons: size,
        activation: model.architecture?.activation || 'ReLU',
        parameters: layerIndex > 0 ? size * layerSizes[layerIndex - 1] : 0,
        outputShape: [size]
      });

      // Create nodes for this layer
      for (let i = 0; i < size; i++) {
        const nodeId = `node-${layerIndex}-${i}`;
        const y = (i - (size - 1) / 2) * config.neuronSpacing;
        const x = layerIndex * config.layerSpacing;
        
        nodeData.push({
          id: nodeId,
          layer: layerIndex,
          position: { 
            x, 
            y, 
            z: config.viewMode === '3d' ? Math.random() * 50 - 25 : 0 
          },
          activation: Math.random(),
          weight: Math.random() * 2 - 1,
          bias: Math.random() * 0.5 - 0.25,
          type: layerIndex === 0 ? 'input' : 
                layerIndex === layerSizes.length - 1 ? 'output' : 'hidden',
          connections: []
        });
      }
    });

    // Create connections
    for (let layerIndex = 0; layerIndex < layerSizes.length - 1; layerIndex++) {
      const currentLayerSize = layerSizes[layerIndex];
      const nextLayerSize = layerSizes[layerIndex + 1];

      for (let i = 0; i < currentLayerSize; i++) {
        for (let j = 0; j < nextLayerSize; j++) {
          const fromId = `node-${layerIndex}-${i}`;
          const toId = `node-${layerIndex + 1}-${j}`;
          const weight = Math.random() * 2 - 1;
          
          const connection: ConnectionData = {
            id: `conn-${fromId}-${toId}`,
            from: fromId,
            to: toId,
            weight,
            strength: Math.abs(weight),
            active: Math.random() > 0.3
          };

          connectionData.push(connection);
          
          // Add to node's connections
          const fromNode = nodeData.find(n => n.id === fromId);
          if (fromNode) {
            fromNode.connections.push(connection);
          }
        }
      }
    }

    setLayers(layerData);
    setNodes(nodeData);
    setConnections(connectionData);
  }, [config.layerSpacing, config.neuronSpacing, config.viewMode]);

  // Generate training data
  const generateTrainingData = useCallback(() => {
    const epochs = Array.from({ length: 100 }, (_, i) => i);
    const loss = epochs.map(e => Math.max(0.01, 2 * Math.exp(-e / 20) + Math.random() * 0.1));
    const accuracy = epochs.map(e => Math.min(0.99, 1 - Math.exp(-e / 15) + Math.random() * 0.05));
    const weights = epochs.map(() => Math.random() * 2 - 1);

    setTrainingData({ epochs, loss, accuracy, weights });
  }, []);

  // Initialize visualization
  useEffect(() => {
    const model = models.find(m => m.id === selectedModel);
    if (model) {
      generateNetworkData(model);
      generateTrainingData();
    }
  }, [selectedModel, models, generateNetworkData, generateTrainingData]);

  // Canvas rendering
  const drawNetwork = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    // Clear canvas
    ctx.fillStyle = 'rgba(0, 0, 0, 0.02)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Center and apply zoom
    ctx.save();
    ctx.translate(rect.width / 2, rect.height / 2);
    ctx.scale(zoom, zoom);

    // Draw connections
    connections.forEach(conn => {
      const fromNode = nodes.find(n => n.id === conn.from);
      const toNode = nodes.find(n => n.id === conn.to);
      
      if (!fromNode || !toNode) return;

      ctx.beginPath();
      ctx.moveTo(fromNode.position.x, fromNode.position.y);
      ctx.lineTo(toNode.position.x, toNode.position.y);
      
      // Color based on weight and config
      if (config.showWeights) {
        const intensity = Math.abs(conn.weight);
        const color = conn.weight > 0 ? `rgba(34, 197, 94, ${intensity * config.connectionOpacity})` : 
                                       `rgba(239, 68, 68, ${intensity * config.connectionOpacity})`;
        ctx.strokeStyle = color;
      } else {
        ctx.strokeStyle = `rgba(100, 116, 139, ${config.connectionOpacity})`;
      }
      
      ctx.lineWidth = conn.active ? Math.max(0.5, conn.strength * 2) : 0.2;
      ctx.stroke();
    });

    // Draw nodes
    nodes.forEach(node => {
      const isSelected = node.id === selectedNode;
      const isHovered = node.id === hoveredNode;
      
      ctx.beginPath();
      const radius = (config.nodeSize + (isSelected ? 4 : isHovered ? 2 : 0)) * 
                    (config.showActivations ? (1 + node.activation) : 1);
      ctx.arc(node.position.x, node.position.y, radius, 0, 2 * Math.PI);
      
      // Color based on type and activation
      let fillColor = '#64748b';
      if (config.colorScheme === 'default') {
        switch (node.type) {
          case 'input': fillColor = '#3b82f6'; break;
          case 'output': fillColor = '#ef4444'; break;
          case 'hidden': fillColor = '#8b5cf6'; break;
        }
      } else if (config.colorScheme === 'heatmap') {
        const intensity = node.activation;
        fillColor = `hsl(${(1 - intensity) * 240}, 70%, 50%)`;
      }
      
      if (config.showActivations) {
        const alpha = 0.3 + node.activation * 0.7;
        ctx.fillStyle = fillColor + Math.floor(alpha * 255).toString(16).padStart(2, '0');
      } else {
        ctx.fillStyle = fillColor;
      }
      
      ctx.fill();
      
      // Border
      ctx.strokeStyle = isSelected ? '#fbbf24' : isHovered ? '#60a5fa' : '#374151';
      ctx.lineWidth = isSelected ? 2 : isHovered ? 1.5 : 1;
      ctx.stroke();
      
      // Labels
      if (config.showLabels && (isSelected || isHovered || node.layer === 0 || node.layer === layers.length - 1)) {
        ctx.fillStyle = '#1f2937';
        ctx.font = '10px system-ui';
        ctx.textAlign = 'center';
        ctx.fillText(
          `${node.activation.toFixed(2)}`, 
          node.position.x, 
          node.position.y - radius - 5
        );
      }
    });

    ctx.restore();
  }, [nodes, connections, layers, config, zoom, selectedNode, hoveredNode]);

  // Animation loop
  useEffect(() => {
    if (isPlaying) {
      const animate = () => {
        // Update activations
        setNodes(prev => prev.map(node => ({
          ...node,
          activation: Math.max(0, Math.min(1, node.activation + (Math.random() - 0.5) * 0.1))
        })));

        drawNetwork();
        animationRef.current = requestAnimationFrame(animate);
      };
      animationRef.current = requestAnimationFrame(animate);
    } else {
      drawNetwork();
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isPlaying, drawNetwork]);

  // Handle canvas interactions
  const handleCanvasClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = (event.clientX - rect.left - rect.width / 2) / zoom;
    const y = (event.clientY - rect.top - rect.height / 2) / zoom;

    // Find clicked node
    const clickedNode = nodes.find(node => {
      const distance = Math.sqrt(
        Math.pow(x - node.position.x, 2) + Math.pow(y - node.position.y, 2)
      );
      return distance <= (config.nodeSize + node.activation * 5);
    });

    setSelectedNode(clickedNode?.id || null);
  }, [nodes, zoom, config.nodeSize]);

  const handleCanvasMouseMove = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = (event.clientX - rect.left - rect.width / 2) / zoom;
    const y = (event.clientY - rect.top - rect.height / 2) / zoom;

    // Find hovered node
    const hoveredNode = nodes.find(node => {
      const distance = Math.sqrt(
        Math.pow(x - node.position.x, 2) + Math.pow(y - node.position.y, 2)
      );
      return distance <= (config.nodeSize + node.activation * 5);
    });

    setHoveredNode(hoveredNode?.id || null);
  }, [nodes, zoom, config.nodeSize]);

  // Export visualization
  const exportVisualization = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');
    link.download = `neural-network-${selectedModel || 'visualization'}.png`;
    link.href = canvas.toDataURL();
    link.click();
  }, [selectedModel]);

  // Reset view
  const resetView = () => {
    setZoom(1);
    setRotation({ x: 0, y: 0, z: 0 });
    setSelectedNode(null);
    setHoveredNode(null);
  };

  const selectedModelData = models.find(m => m.id === selectedModel);

  return (
    <div className="h-[80vh] w-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-3">
          <Network className="h-5 w-5 text-purple-500" />
          <div>
            <h3 className="font-semibold">Neural Network Visualization</h3>
            <p className="text-sm text-muted-foreground">
              Interactive architecture and training visualization
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedModel || ''} onValueChange={onModelSelect}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Select a model" />
            </SelectTrigger>
            <SelectContent>
              {models.map(model => (
                <SelectItem key={model.id} value={model.id}>
                  {model.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={exportVisualization}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Controls Panel */}
        <div className="w-80 border-r bg-muted/20 p-4 space-y-4">
          <ScrollArea className="h-full">
            {/* Playback Controls */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Playback</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="flex-1"
                  >
                    {isPlaying ? (
                      <><Pause className="h-4 w-4 mr-2" /> Pause</>
                    ) : (
                      <><Play className="h-4 w-4 mr-2" /> Play</>
                    )}
                  </Button>
                  <Button size="sm" variant="outline" onClick={resetView}>
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Zoom: {zoom.toFixed(1)}x</Label>
                  <div className="flex items-center gap-2">
                    <Button size="sm" variant="outline" onClick={() => setZoom(Math.max(0.1, zoom - 0.2))}>
                      <ZoomOut className="h-3 w-3" />
                    </Button>
                    <Slider
                      value={[zoom]}
                      onValueChange={([value]) => setZoom(value)}
                      min={0.1}
                      max={3}
                      step={0.1}
                      className="flex-1"
                    />
                    <Button size="sm" variant="outline" onClick={() => setZoom(Math.min(3, zoom + 0.2))}>
                      <ZoomIn className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Visualization Settings */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Visualization</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <Label className="text-xs">View Mode</Label>
                  <Select value={config.viewMode} onValueChange={(value: any) => 
                    setConfig(prev => ({ ...prev, viewMode: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="2d">2D Network</SelectItem>
                      <SelectItem value="3d">3D Network</SelectItem>
                      <SelectItem value="graph">Graph View</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-xs">Color Scheme</Label>
                  <Select value={config.colorScheme} onValueChange={(value: any) => 
                    setConfig(prev => ({ ...prev, colorScheme: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Default</SelectItem>
                      <SelectItem value="heatmap">Heatmap</SelectItem>
                      <SelectItem value="categorical">Categorical</SelectItem>
                      <SelectItem value="gradient">Gradient</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Show Weights</Label>
                    <Switch
                      checked={config.showWeights}
                      onCheckedChange={(checked) => setConfig(prev => ({ ...prev, showWeights: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Show Activations</Label>
                    <Switch
                      checked={config.showActivations}
                      onCheckedChange={(checked) => setConfig(prev => ({ ...prev, showActivations: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Show Labels</Label>
                    <Switch
                      checked={config.showLabels}
                      onCheckedChange={(checked) => setConfig(prev => ({ ...prev, showLabels: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Animate Forward Pass</Label>
                    <Switch
                      checked={config.animateForward}
                      onCheckedChange={(checked) => setConfig(prev => ({ ...prev, animateForward: checked }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-xs">Node Size</Label>
                  <Slider
                    value={[config.nodeSize]}
                    onValueChange={([value]) => setConfig(prev => ({ ...prev, nodeSize: value }))}
                    min={2}
                    max={20}
                    step={1}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-xs">Connection Opacity</Label>
                  <Slider
                    value={[config.connectionOpacity]}
                    onValueChange={([value]) => setConfig(prev => ({ ...prev, connectionOpacity: value }))}
                    min={0.1}
                    max={1}
                    step={0.1}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Model Information */}
            {selectedModelData && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Model Info</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="text-xs">
                    <div className="flex justify-between mb-1">
                      <span className="text-muted-foreground">Type:</span>
                      <Badge variant="outline">{selectedModelData.type}</Badge>
                    </div>
                    <div className="flex justify-between mb-1">
                      <span className="text-muted-foreground">Layers:</span>
                      <span>{layers.length}</span>
                    </div>
                    <div className="flex justify-between mb-1">
                      <span className="text-muted-foreground">Parameters:</span>
                      <span>{layers.reduce((sum, layer) => sum + layer.parameters, 0).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between mb-1">
                      <span className="text-muted-foreground">Accuracy:</span>
                      <span>{selectedModelData.accuracy ? `${(selectedModelData.accuracy * 100).toFixed(1)}%` : 'N/A'}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Layer Details */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Layer Details</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-32">
                  <div className="space-y-2">
                    {layers.map((layer, index) => (
                      <div key={layer.id} className="text-xs p-2 rounded border bg-muted/20">
                        <div className="flex justify-between items-center mb-1">
                          <span className="font-medium">Layer {index + 1}</span>
                          <Badge variant="outline" className="text-xs">
                            {layer.type}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Neurons:</span>
                          <span>{layer.neurons}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Params:</span>
                          <span>{layer.parameters.toLocaleString()}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Selected Node Details */}
            {selectedNode && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Node Details</CardTitle>
                </CardHeader>
                <CardContent>
                  {(() => {
                    const node = nodes.find(n => n.id === selectedNode);
                    if (!node) return null;
                    
                    return (
                      <div className="space-y-2 text-xs">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">ID:</span>
                          <span className="font-mono">{node.id}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Type:</span>
                          <Badge variant="outline">{node.type}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Activation:</span>
                          <span>{node.activation.toFixed(4)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Weight:</span>
                          <span>{node.weight?.toFixed(4) || 'N/A'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Bias:</span>
                          <span>{node.bias?.toFixed(4) || 'N/A'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Connections:</span>
                          <span>{node.connections.length}</span>
                        </div>
                      </div>
                    );
                  })()}
                </CardContent>
              </Card>
            )}
          </ScrollArea>
        </div>

        {/* Visualization Area */}
        <div className="flex-1 relative">
          <canvas
            ref={canvasRef}
            className="w-full h-full cursor-crosshair"
            onClick={handleCanvasClick}
            onMouseMove={handleCanvasMouseMove}
          />
          
          {/* Overlay Information */}
          <div className="absolute top-4 right-4 space-y-2">
            <Card className="p-2">
              <div className="text-xs space-y-1">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Nodes:</span>
                  <span>{nodes.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Connections:</span>
                  <span>{connections.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Zoom:</span>
                  <span>{zoom.toFixed(1)}x</span>
                </div>
              </div>
            </Card>
            
            {isPlaying && (
              <Card className="p-2">
                <div className="flex items-center gap-2">
                  <Activity className="h-3 w-3 text-green-500 animate-pulse" />
                  <span className="text-xs text-green-500">Live Animation</span>
                </div>
              </Card>
            )}
          </div>

          {/* Instructions */}
          <div className="absolute bottom-4 left-4">
            <Card className="p-2">
              <div className="text-xs text-muted-foreground space-y-1">
                <div>• Click nodes to inspect details</div>
                <div>• Use controls to adjust visualization</div>
                <div>• Export to save as image</div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClaudeFlowNeuralVisualization;