/**
 * Claude-Flow v2.0.0 Integration Test Suite
 * Comprehensive tests for all integration points
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { ClaudeFlowIntegrationV2, DEFAULT_CLAUDE_FLOW_V2_CONFIG } from '../lib/claudeFlowIntegrationV2';
import { ClaudeFlowMemoryManager } from '../lib/claudeFlowMemoryManager';
import { MCPClient } from '../lib/mcpClient';
import { CLAUDE_FLOW_MCP_TOOLS } from '../config/claudeFlowMCPTools';
import type { 
  <PERSON><PERSON>lowAgent, 
  ClaudeFlowObjective,
  NeuralModel,
  PerformanceReport
} from '../types/claudeFlowV2';

// Mock the MCP client
jest.mock('../lib/mcpClient');

describe('Claude-Flow v2.0.0 Integration Tests', () => {
  let integration: ClaudeFlowIntegrationV2;
  let mockMCPClient: jest.Mocked<MCPClient>;
  let memoryManager: ClaudeFlowMemoryManager;
  
  beforeEach(() => {
    // Create mock MCP client
    mockMCPClient = {
      initialize: jest.fn().mockResolvedValue(undefined),
      invoke: jest.fn(),
      listTools: jest.fn().mockResolvedValue([]),
      close: jest.fn()
    } as any;
    
    // Create integration instance
    integration = new ClaudeFlowIntegrationV2(DEFAULT_CLAUDE_FLOW_V2_CONFIG);
    
    // Create memory manager with test database
    memoryManager = new ClaudeFlowMemoryManager({
      databasePath: ':memory:', // Use in-memory database for tests
      backupInterval: 0 // Disable auto-backup for tests
    });
  });
  
  afterEach(() => {
    memoryManager.close();
    jest.clearAllMocks();
  });
  
  describe('Core Infrastructure', () => {
    it('should initialize with all 87 MCP tools', async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: { swarmId: 'test-swarm-123' }
      });
      
      await integration.initialize(mockMCPClient);
      
      expect(mockMCPClient.initialize).toHaveBeenCalled();
      expect(mockMCPClient.invoke).toHaveBeenCalledWith('swarm_init', expect.any(Object));
    });
    
    it('should verify all tool categories are present', () => {
      const toolCount = Object.keys(CLAUDE_FLOW_MCP_TOOLS).length;
      expect(toolCount).toBe(87);
      
      // Verify tool categories
      const categories = new Set(
        Object.values(CLAUDE_FLOW_MCP_TOOLS).map(tool => tool.category)
      );
      
      expect(categories).toContain('swarm-orchestration');
      expect(categories).toContain('neural-cognitive');
      expect(categories).toContain('memory-management');
      expect(categories).toContain('performance-monitoring');
      expect(categories).toContain('github-integration');
      expect(categories).toContain('dynamic-agent-architecture');
      expect(categories).toContain('system-security');
    });
  });
  
  describe('Swarm Orchestration', () => {
    beforeEach(async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: { swarmId: 'test-swarm' }
      });
      
      await integration.initialize(mockMCPClient);
    });
    
    it('should create swarm objective', async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: { objectiveId: 'obj-123' }
      });
      
      const objective = await integration.createSwarmObjective({
        title: 'Test Objective',
        description: 'Test Description',
        tasks: [
          { type: 'test', description: 'Test task', priority: 'high' }
        ],
        strategy: 'adaptive'
      });
      
      expect(objective.id).toBe('obj-123');
      expect(objective.status).toBe('pending');
      expect(mockMCPClient.invoke).toHaveBeenCalledWith('swarm/create-objective', expect.any(Object));
    });
    
    it('should execute swarm objective', async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: {}
      });
      
      await integration.executeSwarmObjective('obj-123');
      
      expect(mockMCPClient.invoke).toHaveBeenCalledWith('swarm/execute-objective', {
        objectiveId: 'obj-123'
      });
    });
    
    it('should get comprehensive swarm status', async () => {
      const mockStatus = {
        swarmId: 'test-swarm',
        activeAgents: 5,
        runningTasks: 10,
        completedTasks: 25
      };
      
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: mockStatus
      });
      
      const status = await integration.getSwarmStatus(true);
      
      expect(status).toEqual(mockStatus);
      expect(mockMCPClient.invoke).toHaveBeenCalledWith('swarm/get-status', {
        includeDetails: true
      });
    });
    
    it('should handle emergency stop', async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: {}
      });
      
      await integration.emergencyStop('Critical error detected');
      
      expect(mockMCPClient.invoke).toHaveBeenCalledWith('swarm/emergency-stop', {
        reason: 'Critical error detected',
        force: true
      });
    });
  });
  
  describe('Neural Network Operations', () => {
    beforeEach(async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: { swarmId: 'test-swarm' }
      });
      
      await integration.initialize(mockMCPClient);
    });
    
    it('should train neural model', async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: {
          modelId: 'model-123',
          accuracy: 0.95
        }
      });
      
      const model = await integration.trainNeuralModel({
        pattern: 'test-pattern',
        data: { samples: [] },
        epochs: 100,
        learningRate: 0.001
      });
      
      expect(model.id).toBe('model-123');
      expect(model.accuracy).toBe(0.95);
      expect(model.trainedEpochs).toBe(100);
    });
    
    it('should make neural predictions', async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: {
          prediction: 'category-A',
          confidence: 0.89
        }
      });
      
      const result = await integration.predictWithNeural('model-123', { input: 'test' });
      
      expect(result.prediction).toBe('category-A');
      expect(result.confidence).toBe(0.89);
    });
    
    it('should analyze cognitive patterns', async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: {
          patterns: ['pattern1', 'pattern2'],
          insights: ['insight1']
        }
      });
      
      const analysis = await integration.analyzeCognitive('user-behavior', { context: 'test' });
      
      expect(analysis.patterns).toHaveLength(2);
      expect(analysis.insights).toHaveLength(1);
    });
  });
  
  describe('Dynamic Agent Architecture', () => {
    beforeEach(async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: { swarmId: 'test-swarm' }
      });
      
      await integration.initialize(mockMCPClient);
    });
    
    it('should create dynamic agent', async () => {
      const mockAgent: ClaudeFlowAgent = {
        id: 'agent-123',
        name: 'Test Agent',
        type: 'specialist',
        status: 'idle',
        capabilities: ['analysis', 'reporting'],
        maxConcurrentTasks: 5,
        priority: 8,
        createdAt: new Date().toISOString(),
        swarmId: 'test-swarm'
      };
      
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: { agent: mockAgent }
      });
      
      const agent = await integration.createDynamicAgent({
        type: 'specialist',
        capabilities: ['analysis', 'reporting'],
        resources: { memory: 2048, compute: 'high', priority: 8 }
      });
      
      expect(agent.id).toBe('agent-123');
      expect(agent.capabilities).toContain('analysis');
      expect(agent.capabilities).toContain('reporting');
    });
    
    it('should match agent capabilities', async () => {
      const mockAgents = [
        { id: 'agent-1', capabilities: ['web-search', 'analysis'] },
        { id: 'agent-2', capabilities: ['analysis', 'reporting'] }
      ];
      
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: { matchedAgents: mockAgents }
      });
      
      const matched = await integration.matchCapabilities(['analysis', 'reporting']);
      
      expect(matched).toHaveLength(2);
      expect(mockMCPClient.invoke).toHaveBeenCalledWith('daa/capability-match', {
        'task-requirements': JSON.stringify(['analysis', 'reporting'])
      });
    });
  });
  
  describe('Memory Management', () => {
    it('should store memory entries', async () => {
      const memoryId = await memoryManager.storeMemory({
        agentId: 'agent-123',
        sessionId: 'session-456',
        type: 'observation',
        content: 'Test observation',
        tags: ['test', 'observation']
      });
      
      expect(memoryId).toMatch(/^mem_/);
    });
    
    it('should query memories with filters', async () => {
      // Store test memories
      await memoryManager.storeMemory({
        agentId: 'agent-123',
        sessionId: 'session-456',
        type: 'insight',
        content: 'Important insight',
        tags: ['important']
      });
      
      await memoryManager.storeMemory({
        agentId: 'agent-123',
        sessionId: 'session-456',
        type: 'observation',
        content: 'Regular observation',
        tags: ['regular']
      });
      
      // Query insights
      const insights = await memoryManager.queryMemories({
        filters: [
          { field: 'type', operator: 'eq', value: 'insight' }
        ],
        limit: 10
      });
      
      expect(insights).toHaveLength(1);
      expect(insights[0].type).toBe('insight');
    });
    
    it('should manage namespaces', async () => {
      const namespaceId = await memoryManager.createNamespace({
        name: 'test-namespace',
        permissions: {
          read: ['agent-123'],
          write: ['agent-123'],
          delete: ['admin']
        }
      });
      
      expect(namespaceId).toMatch(/^ns_/);
    });
    
    it('should provide memory statistics', async () => {
      // Store some test data
      await memoryManager.storeMemory({
        agentId: 'agent-123',
        sessionId: 'session-456',
        type: 'observation',
        content: 'Test content',
        tags: []
      });
      
      const stats = await memoryManager.getMemoryStats();
      
      expect(stats.totalEntries).toBeGreaterThan(0);
      expect(stats.byType).toHaveProperty('observation');
    });
  });
  
  describe('Performance Monitoring', () => {
    beforeEach(async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: { swarmId: 'test-swarm' }
      });
      
      await integration.initialize(mockMCPClient);
    });
    
    it('should get performance report', async () => {
      const mockReport: PerformanceReport = {
        timestamp: new Date().toISOString(),
        metrics: {
          avgResponseTime: 150,
          throughput: 1000,
          errorRate: 0.01,
          tokenUsage: 50000,
          costEstimate: 2.5
        },
        bottlenecks: [],
        trends: {
          responseTimeTrend: 'improving',
          throughputTrend: 'stable'
        }
      };
      
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: mockReport
      });
      
      const report = await integration.getPerformanceReport('1h');
      
      expect(report.metrics.avgResponseTime).toBe(150);
      expect(report.trends.responseTimeTrend).toBe('improving');
    });
    
    it('should analyze bottlenecks', async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: {
          bottlenecks: [
            {
              component: 'memory-query',
              severity: 7,
              impact: 'High latency on memory searches',
              recommendation: 'Add indexes to frequently queried fields'
            }
          ]
        }
      });
      
      const analysis = await integration.analyzeBottlenecks();
      
      expect(analysis.bottlenecks).toHaveLength(1);
      expect(analysis.bottlenecks[0].component).toBe('memory-query');
    });
  });
  
  describe('Error Handling', () => {
    beforeEach(async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: { swarmId: 'test-swarm' }
      });
      
      await integration.initialize(mockMCPClient);
    });
    
    it('should handle MCP errors gracefully', async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: false,
        error: 'Connection timeout'
      });
      
      await expect(
        integration.createSwarmObjective({
          title: 'Test',
          description: 'Test',
          tasks: []
        })
      ).rejects.toThrow('Connection timeout');
    });
    
    it('should handle missing MCP client', async () => {
      const uninitializedIntegration = new ClaudeFlowIntegrationV2(DEFAULT_CLAUDE_FLOW_V2_CONFIG);
      
      await expect(
        uninitializedIntegration.getSystemStatus()
      ).rejects.toThrow('Claude-Flow integration not initialized');
    });
  });
  
  describe('Integration Benchmarks', () => {
    beforeEach(async () => {
      mockMCPClient.invoke.mockResolvedValueOnce({
        success: true,
        result: { swarmId: 'test-swarm' }
      });
      
      await integration.initialize(mockMCPClient);
    });
    
    it('should handle high-volume memory operations', async () => {
      const startTime = Date.now();
      const promises = [];
      
      // Store 100 memories
      for (let i = 0; i < 100; i++) {
        promises.push(
          memoryManager.storeMemory({
            agentId: `agent-${i % 10}`,
            sessionId: 'perf-test',
            type: i % 2 === 0 ? 'observation' : 'insight',
            content: `Performance test entry ${i}`,
            tags: [`perf`, `batch-${Math.floor(i / 10)}`]
          })
        );
      }
      
      await Promise.all(promises);
      const storeTime = Date.now() - startTime;
      
      // Query all memories
      const queryStart = Date.now();
      const results = await memoryManager.queryMemories({ limit: 100 });
      const queryTime = Date.now() - queryStart;
      
      expect(results).toHaveLength(100);
      expect(storeTime).toBeLessThan(1000); // Should complete in under 1 second
      expect(queryTime).toBeLessThan(100); // Queries should be fast
    });
    
    it('should handle concurrent swarm operations', async () => {
      // Mock successful responses for all operations
      mockMCPClient.invoke.mockResolvedValue({
        success: true,
        result: { objectiveId: 'obj-concurrent' }
      });
      
      const operations = [];
      
      // Create 10 concurrent objectives
      for (let i = 0; i < 10; i++) {
        operations.push(
          integration.createSwarmObjective({
            title: `Concurrent Objective ${i}`,
            description: 'Test concurrent operations',
            tasks: [
              { type: 'test', description: 'Concurrent task', priority: 'normal' }
            ]
          })
        );
      }
      
      const startTime = Date.now();
      const results = await Promise.all(operations);
      const duration = Date.now() - startTime;
      
      expect(results).toHaveLength(10);
      expect(duration).toBeLessThan(500); // Should handle concurrency efficiently
    });
  });
});