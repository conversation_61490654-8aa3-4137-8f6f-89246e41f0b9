/**
 * Claude-Flow v2.0.0 MCP Tools Configuration
 * Complete definition of all 87 MCP tools with proper categorization
 */

export interface MCPToolDefinition {
  name: string;
  category: string;
  description: string;
  inputSchema: any;
  outputSchema?: any;
  examples?: string[];
}

export const CLAUDE_FLOW_MCP_TOOLS: Record<string, MCPToolDefinition> = {
  // ============= SWARM ORCHESTRATION (15 tools) =============
  'swarm_init': {
    name: 'swarm_init',
    category: 'swarm-orchestration',
    description: 'Initialize a new swarm with hive-mind coordination',
    inputSchema: {
      type: 'object',
      properties: {
        enableQueen: { type: 'boolean', default: true },
        enableNeuralCoordination: { type: 'boolean', default: true },
        securityLevel: { type: 'string', enum: ['low', 'normal', 'high'], default: 'normal' }
      }
    }
  },
  'swarm/create-objective': {
    name: 'swarm/create-objective',
    category: 'swarm-orchestration',
    description: 'Create a new swarm objective with tasks and coordination',
    inputSchema: {
      type: 'object',
      properties: {
        title: { type: 'string' },
        description: { type: 'string' },
        tasks: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: { type: 'string' },
              description: { type: 'string' },
              requirements: { type: 'object' },
              priority: { type: 'string', enum: ['low', 'normal', 'high', 'critical'] }
            }
          }
        },
        strategy: { type: 'string', enum: ['parallel', 'sequential', 'adaptive'] },
        timeout: { type: 'number' }
      },
      required: ['title', 'description', 'tasks']
    }
  },
  'swarm/execute-objective': {
    name: 'swarm/execute-objective',
    category: 'swarm-orchestration',
    description: 'Execute a swarm objective',
    inputSchema: {
      type: 'object',
      properties: {
        objectiveId: { type: 'string' }
      },
      required: ['objectiveId']
    }
  },
  'swarm/get-status': {
    name: 'swarm/get-status',
    category: 'swarm-orchestration',
    description: 'Get comprehensive swarm status',
    inputSchema: {
      type: 'object',
      properties: {
        includeDetails: { type: 'boolean', default: false }
      }
    }
  },
  'swarm/get-comprehensive-status': {
    name: 'swarm/get-comprehensive-status',
    category: 'swarm-orchestration',
    description: 'Get comprehensive status of the entire swarm system',
    inputSchema: {
      type: 'object',
      properties: {}
    }
  },
  'swarm/emergency-stop': {
    name: 'swarm/emergency-stop',
    category: 'swarm-orchestration',
    description: 'Emergency stop of all swarm operations',
    inputSchema: {
      type: 'object',
      properties: {
        reason: { type: 'string' },
        force: { type: 'boolean', default: false }
      },
      required: ['reason']
    }
  },
  'agent_spawn': {
    name: 'agent_spawn',
    category: 'swarm-orchestration',
    description: 'Spawn a new agent in the swarm',
    inputSchema: {
      type: 'object',
      properties: {
        type: { type: 'string' },
        name: { type: 'string' },
        capabilities: { type: 'array', items: { type: 'string' } }
      },
      required: ['type', 'name']
    }
  },
  'task_orchestrate': {
    name: 'task_orchestrate',
    category: 'swarm-orchestration',
    description: 'Orchestrate task distribution across swarm',
    inputSchema: {
      type: 'object',
      properties: {
        taskId: { type: 'string' },
        strategy: { type: 'string' }
      },
      required: ['taskId']
    }
  },
  'swarm_monitor': {
    name: 'swarm_monitor',
    category: 'swarm-orchestration',
    description: 'Monitor swarm health and performance',
    inputSchema: {
      type: 'object',
      properties: {
        metrics: { type: 'array', items: { type: 'string' } }
      }
    }
  },
  'topology_optimize': {
    name: 'topology_optimize',
    category: 'swarm-orchestration',
    description: 'Optimize swarm topology for efficiency',
    inputSchema: {
      type: 'object',
      properties: {
        targetMetric: { type: 'string', enum: ['latency', 'throughput', 'resilience'] }
      }
    }
  },
  'load_balance': {
    name: 'load_balance',
    category: 'swarm-orchestration',
    description: 'Balance load across swarm agents',
    inputSchema: {
      type: 'object',
      properties: {
        algorithm: { type: 'string', enum: ['round-robin', 'least-loaded', 'weighted'] }
      }
    }
  },
  'coordination_sync': {
    name: 'coordination_sync',
    category: 'swarm-orchestration',
    description: 'Synchronize coordination across swarm',
    inputSchema: {
      type: 'object',
      properties: {
        syncType: { type: 'string', enum: ['full', 'partial', 'delta'] }
      }
    }
  },
  'swarm_scale': {
    name: 'swarm_scale',
    category: 'swarm-orchestration',
    description: 'Scale swarm up or down based on demand',
    inputSchema: {
      type: 'object',
      properties: {
        targetSize: { type: 'number' },
        scaleStrategy: { type: 'string', enum: ['gradual', 'immediate'] }
      },
      required: ['targetSize']
    }
  },
  'swarm_destroy': {
    name: 'swarm_destroy',
    category: 'swarm-orchestration',
    description: 'Safely destroy swarm and clean up resources',
    inputSchema: {
      type: 'object',
      properties: {
        preserveData: { type: 'boolean', default: true }
      }
    }
  },
  'dispatch_agent': {
    name: 'dispatch_agent',
    category: 'swarm-orchestration',
    description: 'Dispatch agent for specific task (legacy)',
    inputSchema: {
      type: 'object',
      properties: {
        type: { type: 'string' },
        task: { type: 'string' },
        name: { type: 'string' }
      },
      required: ['type', 'task']
    }
  },

  // ============= NEURAL & COGNITIVE (12 tools) =============
  'neural/train': {
    name: 'neural/train',
    category: 'neural-cognitive',
    description: 'Train a neural network model',
    inputSchema: {
      type: 'object',
      properties: {
        pattern: { type: 'string' },
        data: { type: 'object' },
        epochs: { type: 'number', default: 50 },
        learningRate: { type: 'number', default: 0.001 },
        batchSize: { type: 'number', default: 32 }
      },
      required: ['pattern']
    }
  },
  'neural/predict': {
    name: 'neural/predict',
    category: 'neural-cognitive',
    description: 'Make predictions using neural model',
    inputSchema: {
      type: 'object',
      properties: {
        model: { type: 'string' },
        input: { type: 'object' }
      },
      required: ['model', 'input']
    }
  },
  'neural_train': {
    name: 'neural_train',
    category: 'neural-cognitive',
    description: 'Train neural network (alternative endpoint)',
    inputSchema: {
      type: 'object',
      properties: {
        modelType: { type: 'string' },
        trainingData: { type: 'array' }
      },
      required: ['modelType', 'trainingData']
    }
  },
  'neural_predict': {
    name: 'neural_predict',
    category: 'neural-cognitive',
    description: 'Neural prediction (alternative endpoint)',
    inputSchema: {
      type: 'object',
      properties: {
        modelId: { type: 'string' },
        inputData: { type: 'object' }
      },
      required: ['modelId', 'inputData']
    }
  },
  'pattern_recognize': {
    name: 'pattern_recognize',
    category: 'neural-cognitive',
    description: 'Recognize patterns in data',
    inputSchema: {
      type: 'object',
      properties: {
        data: { type: 'array' },
        patternType: { type: 'string' }
      },
      required: ['data']
    }
  },
  'cognitive/analyze': {
    name: 'cognitive/analyze',
    category: 'neural-cognitive',
    description: 'Analyze cognitive patterns and behaviors',
    inputSchema: {
      type: 'object',
      properties: {
        behavior: { type: 'string' },
        context: { type: 'object' }
      },
      required: ['behavior']
    }
  },
  'cognitive_analyze': {
    name: 'cognitive_analyze',
    category: 'neural-cognitive',
    description: 'Cognitive analysis (alternative endpoint)',
    inputSchema: {
      type: 'object',
      properties: {
        target: { type: 'string' },
        depth: { type: 'string', enum: ['shallow', 'medium', 'deep'] }
      },
      required: ['target']
    }
  },
  'learning_adapt': {
    name: 'learning_adapt',
    category: 'neural-cognitive',
    description: 'Adaptive learning from experiences',
    inputSchema: {
      type: 'object',
      properties: {
        experience: { type: 'object' },
        adaptationType: { type: 'string' }
      },
      required: ['experience']
    }
  },
  'neural_compress': {
    name: 'neural_compress',
    category: 'neural-cognitive',
    description: 'Compress neural models for efficiency',
    inputSchema: {
      type: 'object',
      properties: {
        modelId: { type: 'string' },
        compressionRatio: { type: 'number' }
      },
      required: ['modelId']
    }
  },
  'ensemble_create': {
    name: 'ensemble_create',
    category: 'neural-cognitive',
    description: 'Create ensemble of neural models',
    inputSchema: {
      type: 'object',
      properties: {
        models: { type: 'array', items: { type: 'string' } },
        strategy: { type: 'string', enum: ['voting', 'stacking', 'bagging'] }
      },
      required: ['models']
    }
  },
  'transfer_learn': {
    name: 'transfer_learn',
    category: 'neural-cognitive',
    description: 'Transfer learning between models',
    inputSchema: {
      type: 'object',
      properties: {
        sourceModel: { type: 'string' },
        targetDomain: { type: 'string' }
      },
      required: ['sourceModel', 'targetDomain']
    }
  },
  'neural_explain': {
    name: 'neural_explain',
    category: 'neural-cognitive',
    description: 'Explain neural network decisions',
    inputSchema: {
      type: 'object',
      properties: {
        modelId: { type: 'string' },
        prediction: { type: 'object' }
      },
      required: ['modelId', 'prediction']
    }
  },

  // ============= MEMORY MANAGEMENT (10 tools) =============
  'memory/usage': {
    name: 'memory/usage',
    category: 'memory-management',
    description: 'Get memory usage statistics',
    inputSchema: {
      type: 'object',
      properties: {
        namespace: { type: 'string' }
      }
    }
  },
  'memory/search': {
    name: 'memory/search',
    category: 'memory-management',
    description: 'Search memory with advanced queries',
    inputSchema: {
      type: 'object',
      properties: {
        query: { type: 'string' },
        filters: { type: 'object' },
        limit: { type: 'number', default: 50 }
      },
      required: ['query']
    }
  },
  'memory/persist': {
    name: 'memory/persist',
    category: 'memory-management',
    description: 'Persist memory to storage',
    inputSchema: {
      type: 'object',
      properties: {
        namespace: { type: 'string' },
        compress: { type: 'boolean', default: true }
      }
    }
  },
  'memory/namespace': {
    name: 'memory/namespace',
    category: 'memory-management',
    description: 'Manage memory namespaces',
    inputSchema: {
      type: 'object',
      properties: {
        action: { type: 'string', enum: ['create', 'delete', 'query', 'list'] },
        namespace: { type: 'string' },
        config: { type: 'object' }
      },
      required: ['action']
    }
  },
  'memory/backup': {
    name: 'memory/backup',
    category: 'memory-management',
    description: 'Backup memory data',
    inputSchema: {
      type: 'object',
      properties: {
        destination: { type: 'string' },
        incremental: { type: 'boolean', default: false }
      }
    }
  },
  'memory/restore': {
    name: 'memory/restore',
    category: 'memory-management',
    description: 'Restore memory from backup',
    inputSchema: {
      type: 'object',
      properties: {
        source: { type: 'string' },
        targetNamespace: { type: 'string' }
      },
      required: ['source']
    }
  },
  'memory/compress': {
    name: 'memory/compress',
    category: 'memory-management',
    description: 'Compress memory for efficiency',
    inputSchema: {
      type: 'object',
      properties: {
        algorithm: { type: 'string', enum: ['gzip', 'lz4', 'zstd'] },
        level: { type: 'number' }
      }
    }
  },
  'memory/sync': {
    name: 'memory/sync',
    category: 'memory-management',
    description: 'Synchronize memory across instances',
    inputSchema: {
      type: 'object',
      properties: {
        target: { type: 'string' },
        bidirectional: { type: 'boolean', default: false }
      },
      required: ['target']
    }
  },
  'memory/analytics': {
    name: 'memory/analytics',
    category: 'memory-management',
    description: 'Analyze memory patterns and usage',
    inputSchema: {
      type: 'object',
      properties: {
        timeRange: { type: 'string' },
        metrics: { type: 'array', items: { type: 'string' } }
      }
    }
  },
  'memory/stats': {
    name: 'memory/stats',
    category: 'memory-management',
    description: 'Get detailed memory statistics',
    inputSchema: {
      type: 'object',
      properties: {}
    }
  },

  // ============= PERFORMANCE & MONITORING (10 tools) =============
  'performance_report': {
    name: 'performance_report',
    category: 'performance-monitoring',
    description: 'Generate comprehensive performance report',
    inputSchema: {
      type: 'object',
      properties: {
        timeRange: { type: 'string', enum: ['1h', '6h', '24h', '7d'] },
        includeRecommendations: { type: 'boolean', default: true }
      }
    }
  },
  'bottleneck_analyze': {
    name: 'bottleneck_analyze',
    category: 'performance-monitoring',
    description: 'Analyze system bottlenecks',
    inputSchema: {
      type: 'object',
      properties: {
        autoOptimize: { type: 'boolean', default: false }
      }
    }
  },
  'token_usage': {
    name: 'token_usage',
    category: 'performance-monitoring',
    description: 'Track token usage and costs',
    inputSchema: {
      type: 'object',
      properties: {
        period: { type: 'string' },
        groupBy: { type: 'string', enum: ['model', 'agent', 'task'] }
      }
    }
  },
  'benchmark_run': {
    name: 'benchmark_run',
    category: 'performance-monitoring',
    description: 'Run performance benchmarks',
    inputSchema: {
      type: 'object',
      properties: {
        suite: { type: 'string' },
        iterations: { type: 'number', default: 3 }
      },
      required: ['suite']
    }
  },
  'metrics_collect': {
    name: 'metrics_collect',
    category: 'performance-monitoring',
    description: 'Collect system metrics',
    inputSchema: {
      type: 'object',
      properties: {
        interval: { type: 'number' },
        duration: { type: 'number' }
      }
    }
  },
  'trend_analysis': {
    name: 'trend_analysis',
    category: 'performance-monitoring',
    description: 'Analyze performance trends',
    inputSchema: {
      type: 'object',
      properties: {
        metric: { type: 'string' },
        period: { type: 'string' }
      },
      required: ['metric']
    }
  },
  'health_check': {
    name: 'health_check',
    category: 'performance-monitoring',
    description: 'Perform system health check',
    inputSchema: {
      type: 'object',
      properties: {
        components: { type: 'array', items: { type: 'string' } },
        autoHeal: { type: 'boolean', default: false }
      }
    }
  },
  'diagnostic_run': {
    name: 'diagnostic_run',
    category: 'performance-monitoring',
    description: 'Run diagnostic tests',
    inputSchema: {
      type: 'object',
      properties: {
        level: { type: 'string', enum: ['basic', 'standard', 'deep'] }
      }
    }
  },
  'usage_stats': {
    name: 'usage_stats',
    category: 'performance-monitoring',
    description: 'Get usage statistics',
    inputSchema: {
      type: 'object',
      properties: {
        resource: { type: 'string' },
        aggregation: { type: 'string' }
      }
    }
  },
  'monitor/get-metrics': {
    name: 'monitor/get-metrics',
    category: 'performance-monitoring',
    description: 'Get monitoring metrics',
    inputSchema: {
      type: 'object',
      properties: {
        type: { type: 'string', enum: ['system', 'swarm', 'agents', 'all'] }
      }
    }
  },

  // ============= WORKFLOW AUTOMATION (10 tools) =============
  'workflow_create': {
    name: 'workflow_create',
    category: 'workflow-automation',
    description: 'Create workflow definition',
    inputSchema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        steps: { type: 'array' },
        parallel: { type: 'boolean', default: false }
      },
      required: ['name', 'steps']
    }
  },
  'workflow_execute': {
    name: 'workflow_execute',
    category: 'workflow-automation',
    description: 'Execute workflow',
    inputSchema: {
      type: 'object',
      properties: {
        workflowId: { type: 'string' },
        parameters: { type: 'object' }
      },
      required: ['workflowId']
    }
  },
  'workflow/export': {
    name: 'workflow/export',
    category: 'workflow-automation',
    description: 'Export workflow definition',
    inputSchema: {
      type: 'object',
      properties: {
        workflowId: { type: 'string' },
        format: { type: 'string', enum: ['json', 'yaml'] }
      },
      required: ['workflowId']
    }
  },
  'automation_setup': {
    name: 'automation_setup',
    category: 'workflow-automation',
    description: 'Setup automation rules',
    inputSchema: {
      type: 'object',
      properties: {
        trigger: { type: 'object' },
        actions: { type: 'array' }
      },
      required: ['trigger', 'actions']
    }
  },
  'pipeline_create': {
    name: 'pipeline_create',
    category: 'workflow-automation',
    description: 'Create processing pipeline',
    inputSchema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        stages: { type: 'array' }
      },
      required: ['name', 'stages']
    }
  },
  'scheduler_manage': {
    name: 'scheduler_manage',
    category: 'workflow-automation',
    description: 'Manage scheduled tasks',
    inputSchema: {
      type: 'object',
      properties: {
        action: { type: 'string', enum: ['create', 'update', 'delete', 'list'] },
        schedule: { type: 'object' }
      },
      required: ['action']
    }
  },
  'trigger_setup': {
    name: 'trigger_setup',
    category: 'workflow-automation',
    description: 'Setup workflow triggers',
    inputSchema: {
      type: 'object',
      properties: {
        type: { type: 'string', enum: ['webhook', 'event', 'schedule'] },
        config: { type: 'object' }
      },
      required: ['type', 'config']
    }
  },
  'batch_process': {
    name: 'batch_process',
    category: 'workflow-automation',
    description: 'Process items in batch',
    inputSchema: {
      type: 'object',
      properties: {
        items: { type: 'array' },
        concurrent: { type: 'boolean', default: true },
        maxConcurrency: { type: 'number', default: 5 }
      },
      required: ['items']
    }
  },
  'parallel_execute': {
    name: 'parallel_execute',
    category: 'workflow-automation',
    description: 'Execute tasks in parallel',
    inputSchema: {
      type: 'object',
      properties: {
        tasks: { type: 'array' },
        maxParallel: { type: 'number' }
      },
      required: ['tasks']
    }
  },

  // ============= GITHUB INTEGRATION (6 tools) =============
  'github/repo-analyze': {
    name: 'github/repo-analyze',
    category: 'github-integration',
    description: 'Analyze GitHub repository',
    inputSchema: {
      type: 'object',
      properties: {
        repoUrl: { type: 'string' },
        analysisType: { type: 'string', enum: ['security', 'quality', 'performance', 'all'] }
      },
      required: ['repoUrl']
    }
  },
  'github/pr-manage': {
    name: 'github/pr-manage',
    category: 'github-integration',
    description: 'Manage GitHub pull requests',
    inputSchema: {
      type: 'object',
      properties: {
        action: { type: 'string', enum: ['review', 'approve', 'merge', 'close'] },
        prUrl: { type: 'string' },
        comment: { type: 'string' }
      },
      required: ['action', 'prUrl']
    }
  },
  'github/issue-track': {
    name: 'github/issue-track',
    category: 'github-integration',
    description: 'Track GitHub issues',
    inputSchema: {
      type: 'object',
      properties: {
        action: { type: 'string', enum: ['create', 'update', 'close', 'assign'] },
        issueData: { type: 'object' }
      },
      required: ['action']
    }
  },
  'github/release-coord': {
    name: 'github/release-coord',
    category: 'github-integration',
    description: 'Coordinate GitHub releases',
    inputSchema: {
      type: 'object',
      properties: {
        version: { type: 'string' },
        autoChangelog: { type: 'boolean', default: true }
      },
      required: ['version']
    }
  },
  'github/workflow-auto': {
    name: 'github/workflow-auto',
    category: 'github-integration',
    description: 'Automate GitHub workflows',
    inputSchema: {
      type: 'object',
      properties: {
        workflow: { type: 'string' },
        parameters: { type: 'object' }
      },
      required: ['workflow']
    }
  },
  'github/code-review': {
    name: 'github/code-review',
    category: 'github-integration',
    description: 'AI-powered code review',
    inputSchema: {
      type: 'object',
      properties: {
        prUrl: { type: 'string' },
        reviewDepth: { type: 'string', enum: ['quick', 'standard', 'thorough'] }
      },
      required: ['prUrl']
    }
  },

  // ============= DYNAMIC AGENT ARCHITECTURE (6 tools) =============
  'daa/agent-create': {
    name: 'daa/agent-create',
    category: 'dynamic-agent-architecture',
    description: 'Create dynamic agent with capabilities',
    inputSchema: {
      type: 'object',
      properties: {
        type: { type: 'string' },
        capabilities: { type: 'string' },
        resources: { type: 'string' },
        securityLevel: { type: 'string', enum: ['low', 'normal', 'high'] }
      },
      required: ['type', 'capabilities']
    }
  },
  'daa/capability-match': {
    name: 'daa/capability-match',
    category: 'dynamic-agent-architecture',
    description: 'Match capabilities to task requirements',
    inputSchema: {
      type: 'object',
      properties: {
        'task-requirements': { type: 'string' }
      },
      required: ['task-requirements']
    }
  },
  'daa/resource-alloc': {
    name: 'daa/resource-alloc',
    category: 'dynamic-agent-architecture',
    description: 'Allocate resources to agents',
    inputSchema: {
      type: 'object',
      properties: {
        agentId: { type: 'string' },
        resources: { type: 'object' }
      },
      required: ['agentId', 'resources']
    }
  },
  'daa/lifecycle-manage': {
    name: 'daa/lifecycle-manage',
    category: 'dynamic-agent-architecture',
    description: 'Manage agent lifecycle',
    inputSchema: {
      type: 'object',
      properties: {
        agentId: { type: 'string' },
        action: { type: 'string', enum: ['scale-up', 'scale-down', 'pause', 'resume', 'restart'] }
      },
      required: ['agentId', 'action']
    }
  },
  'daa/communication': {
    name: 'daa/communication',
    category: 'dynamic-agent-architecture',
    description: 'Manage inter-agent communication',
    inputSchema: {
      type: 'object',
      properties: {
        protocol: { type: 'string' },
        channels: { type: 'array', items: { type: 'string' } }
      }
    }
  },
  'daa/consensus': {
    name: 'daa/consensus',
    category: 'dynamic-agent-architecture',
    description: 'Achieve consensus among agents',
    inputSchema: {
      type: 'object',
      properties: {
        topic: { type: 'string' },
        participants: { type: 'array', items: { type: 'string' } },
        strategy: { type: 'string', enum: ['majority', 'unanimous', 'weighted'] }
      },
      required: ['topic', 'participants']
    }
  },

  // ============= SYSTEM & SECURITY (8 tools) =============
  'security_scan': {
    name: 'security_scan',
    category: 'system-security',
    description: 'Run security vulnerability scan',
    inputSchema: {
      type: 'object',
      properties: {
        target: { type: 'string' },
        deep: { type: 'boolean', default: true },
        autoFix: { type: 'boolean', default: false }
      }
    }
  },
  'backup_create': {
    name: 'backup_create',
    category: 'system-security',
    description: 'Create system backup',
    inputSchema: {
      type: 'object',
      properties: {
        components: { type: 'array', items: { type: 'string' } },
        compress: { type: 'boolean', default: true }
      }
    }
  },
  'restore_system': {
    name: 'restore_system',
    category: 'system-security',
    description: 'Restore from backup',
    inputSchema: {
      type: 'object',
      properties: {
        backupId: { type: 'string' },
        components: { type: 'array', items: { type: 'string' } }
      },
      required: ['backupId']
    }
  },
  'config_manage': {
    name: 'config_manage',
    category: 'system-security',
    description: 'Manage system configuration',
    inputSchema: {
      type: 'object',
      properties: {
        action: { type: 'string', enum: ['get', 'set', 'validate', 'export', 'import'] },
        config: { type: 'object' }
      },
      required: ['action']
    }
  },
  'features_detect': {
    name: 'features_detect',
    category: 'system-security',
    description: 'Detect available features',
    inputSchema: {
      type: 'object',
      properties: {}
    }
  },
  'log_analysis': {
    name: 'log_analysis',
    category: 'system-security',
    description: 'Analyze system logs',
    inputSchema: {
      type: 'object',
      properties: {
        timeRange: { type: 'string' },
        patterns: { type: 'array', items: { type: 'string' } }
      }
    }
  },
  'monitor/get-alerts': {
    name: 'monitor/get-alerts',
    category: 'system-security',
    description: 'Get active security alerts',
    inputSchema: {
      type: 'object',
      properties: {
        level: { type: 'string', enum: ['info', 'warning', 'critical', 'all'] },
        limit: { type: 'number', default: 50 }
      }
    }
  },

  // ============= ADDITIONAL TOOLS =============
  'agent/create': {
    name: 'agent/create',
    category: 'agent-management',
    description: 'Create new agent',
    inputSchema: {
      type: 'object',
      properties: {
        type: { type: 'string' },
        capabilities: { type: 'object' },
        config: { type: 'object' }
      },
      required: ['type']
    }
  },
  'agent/list': {
    name: 'agent/list',
    category: 'agent-management',
    description: 'List all agents',
    inputSchema: {
      type: 'object',
      properties: {
        status: { type: 'string', enum: ['active', 'idle', 'busy', 'failed', 'all'] }
      }
    }
  },
  'resource/register': {
    name: 'resource/register',
    category: 'resource-management',
    description: 'Register new resource',
    inputSchema: {
      type: 'object',
      properties: {
        type: { type: 'string' },
        name: { type: 'string' },
        capacity: { type: 'object' }
      },
      required: ['type', 'name', 'capacity']
    }
  },
  'resource/get-statistics': {
    name: 'resource/get-statistics',
    category: 'resource-management',
    description: 'Get resource statistics',
    inputSchema: {
      type: 'object',
      properties: {}
    }
  },
  'message/send': {
    name: 'message/send',
    category: 'message-bus',
    description: 'Send message through message bus',
    inputSchema: {
      type: 'object',
      properties: {
        type: { type: 'string' },
        content: { type: 'object' },
        sender: { type: 'string' },
        receivers: { type: 'array', items: { type: 'string' } },
        priority: { type: 'string', enum: ['low', 'normal', 'high', 'critical'] }
      },
      required: ['type', 'content', 'sender', 'receivers']
    }
  },
  'message/get-metrics': {
    name: 'message/get-metrics',
    category: 'message-bus',
    description: 'Get message bus metrics',
    inputSchema: {
      type: 'object',
      properties: {}
    }
  }
};

// Tool categories for organization
export const CLAUDE_FLOW_TOOL_CATEGORIES = {
  'swarm-orchestration': {
    name: 'Swarm Orchestration',
    description: 'Hive-mind coordination and swarm management',
    icon: '🐝',
    tools: 15
  },
  'neural-cognitive': {
    name: 'Neural & Cognitive',
    description: 'Neural networks and cognitive computing',
    icon: '🧠',
    tools: 12
  },
  'memory-management': {
    name: 'Memory Management',
    description: 'Advanced memory operations and persistence',
    icon: '💾',
    tools: 10
  },
  'performance-monitoring': {
    name: 'Performance & Monitoring',
    description: 'Performance tracking and system monitoring',
    icon: '📊',
    tools: 10
  },
  'workflow-automation': {
    name: 'Workflow Automation',
    description: 'Workflow creation and automation',
    icon: '🔄',
    tools: 10
  },
  'github-integration': {
    name: 'GitHub Integration',
    description: 'GitHub repository management and automation',
    icon: '📦',
    tools: 6
  },
  'dynamic-agent-architecture': {
    name: 'Dynamic Agent Architecture',
    description: 'Dynamic agent creation and management',
    icon: '🤖',
    tools: 6
  },
  'system-security': {
    name: 'System & Security',
    description: 'Security and system management',
    icon: '🛡️',
    tools: 8
  },
  'agent-management': {
    name: 'Agent Management',
    description: 'Basic agent operations',
    icon: '👥',
    tools: 2
  },
  'resource-management': {
    name: 'Resource Management',
    description: 'Resource allocation and tracking',
    icon: '📈',
    tools: 2
  },
  'message-bus': {
    name: 'Message Bus',
    description: 'Inter-agent communication',
    icon: '💬',
    tools: 2
  }
};

// Export tool count verification
export const TOTAL_CLAUDE_FLOW_TOOLS = Object.keys(CLAUDE_FLOW_MCP_TOOLS).length;
console.log(`Total Claude-Flow v2.0.0 MCP Tools: ${TOTAL_CLAUDE_FLOW_TOOLS}`);